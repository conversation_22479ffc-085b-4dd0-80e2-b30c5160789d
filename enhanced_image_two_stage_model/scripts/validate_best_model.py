#!/usr/bin/env python3
"""
验证最佳模型脚本

加载训练好的最佳模型，在验证集和测试集上重新计算所有评估指标。
"""

import os
import sys
import time
import json
import argparse
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))
sys.path.append(str(project_root / 'R2Gen-main'))
sys.path.append(str(project_root / 'two_stage_model'))

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='验证最佳R2Gen二阶段模型')
    
    parser.add_argument('--model_path', type=str, 
                       default='enhanced_image_two_stage_model/checkpoints/best_model.pth',
                       help='最佳模型路径')
    parser.add_argument('--batch_size', type=int, default=8, help='批次大小')
    
    return parser.parse_args()

def main():
    """主函数"""
    print("🔍 开始验证最佳R2Gen二阶段模型")
    
    # 解析参数
    args = parse_arguments()
    
    print(f"\n📋 验证配置:")
    print(f"  - 模型路径: {args.model_path}")
    print(f"  - 批次大小: {args.batch_size}")
    
    try:
        # 导入现有的训练器
        from stage2_trainer import Stage2Trainer
        
        # 创建训练参数（与训练时保持一致）
        class ValidationArgs:
            # 数据相关
            features_dir = str(project_root / 'two_stage_model' / 'features')
            ann_path = str(project_root / 'R2Gen-main' / 'data' / 'iu_xray' / 'annotation.json')
            dataset_name = 'iu_xray'
            
            # 视觉提取器参数
            visual_extractor = 'resnet101'
            visual_extractor_pretrained = True
            
            # 模型参数
            max_seq_length = 60
            threshold = 3
            num_layers = 3
            d_model = 512
            d_ff = 512
            d_vf = 2048  # patch features dimension
            num_heads = 8
            dropout = 0.1
            num_workers = 2
            
            # 记忆模块参数
            rm_num_slots = 3
            rm_num_heads = 8
            rm_d_model = 512
            
            # 训练参数（验证时不需要，但保持一致）
            batch_size = args.batch_size
            epochs = 5
            lr_ed = 1e-4
            lr_ve = 1e-4
            weight_decay = 5e-5
            step_size = 50
            gamma = 0.1
            grad_clip = 5.0
            
            # 其他参数
            logit_layers = 1
            bos_idx = 0
            eos_idx = 0
            pad_idx = 0
            use_bn = 0
            drop_prob_lm = 0.5
            sample_method = 'beam_search'
            
            # 保存目录
            save_dir = str(project_root / 'enhanced_image_two_stage_model' / 'checkpoints')
            
        validation_args = ValidationArgs()
        
        # 检查模型文件是否存在
        model_path = os.path.join(project_root, args.model_path)
        if not os.path.exists(model_path):
            print(f"❌ 模型文件不存在: {model_path}")
            return None
        
        print(f"\n初始化验证器...")
        trainer = Stage2Trainer(validation_args)
        
        print(f"加载最佳模型: {model_path}")
        trainer.load_checkpoint('best_model.pth')
        
        print(f"\n=== 开始验证 ===")
        
        # 验证集评估
        print(f"\n📊 验证集评估:")
        val_scores = trainer.validate(trainer.val_dataloader, 'val')
        print(f"验证集结果:")
        for metric, score in val_scores.items():
            print(f"  {metric}: {score:.4f}")
        
        # 测试集评估
        print(f"\n📊 测试集评估:")
        test_scores = trainer.validate(trainer.test_dataloader, 'test')
        print(f"测试集结果:")
        for metric, score in test_scores.items():
            print(f"  {metric}: {score:.4f}")
        
        # 保存详细验证结果
        validation_results = {
            'model_path': args.model_path,
            'validation_time': time.strftime('%Y-%m-%d %H:%M:%S'),
            'val_scores': val_scores,
            'test_scores': test_scores,
            'config': {
                'batch_size': args.batch_size,
                'model': 'R2Gen_enhanced_two_stage_best'
            }
        }
        
        results_file = os.path.join(validation_args.save_dir, 'best_model_validation_results.json')
        with open(results_file, 'w') as f:
            json.dump(validation_results, f, indent=2)
        
        print(f"\n✅ 验证结果已保存到: {results_file}")
        
        # 更新对比文件
        update_comparison_file(validation_results)
        
        return validation_results
        
    except Exception as e:
        print(f"\n❌ 验证过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def update_comparison_file(results):
    """更新模型对比文件"""
    try:
        comparison_file = project_root / 'iu_xray_model_comparison.md'
        
        # 添加新的验证结果
        new_entry = f"""
## R2Gen Enhanced Two-Stage - 最佳模型重新验证 (验证时间: {results['validation_time']})

**模型配置:**
- 模型: R2Gen Enhanced Two-Stage (最佳检查点)
- 模型路径: {results['model_path']}
- 批次大小: {results['config']['batch_size']}

**验证集完整结果:**
"""
        
        if results['val_scores']:
            # 按指标重要性排序显示
            metric_order = ['BLEU_1', 'BLEU_2', 'BLEU_3', 'BLEU_4', 'METEOR', 'ROUGE_L', 'CIDEr', 'loss']
            for metric in metric_order:
                if metric in results['val_scores']:
                    score = results['val_scores'][metric]
                    new_entry += f"- {metric}: {score:.4f}\n"
        
        new_entry += "\n**测试集完整结果:**\n"
        
        if results['test_scores']:
            # 按指标重要性排序显示
            metric_order = ['BLEU_1', 'BLEU_2', 'BLEU_3', 'BLEU_4', 'METEOR', 'ROUGE_L', 'CIDEr', 'loss']
            for metric in metric_order:
                if metric in results['test_scores']:
                    score = results['test_scores'][metric]
                    new_entry += f"- {metric}: {score:.4f}\n"
        
        new_entry += "\n---\n"
        
        # 写入文件
        with open(comparison_file, 'a', encoding='utf-8') as f:
            f.write(new_entry)
        
        print(f"✅ 验证结果已添加到对比文件: {comparison_file}")
        
    except Exception as e:
        print(f"⚠️ 更新对比文件失败: {e}")

if __name__ == '__main__':
    results = main()
    
    if results is not None:
        print("\n🎉 最佳模型验证完成!")
        print(f"验证集BLEU-4: {results['val_scores']['BLEU_4']:.4f}")
        print(f"测试集BLEU-4: {results['test_scores']['BLEU_4']:.4f}")
    else:
        print("\n💥 验证失败!")
        sys.exit(1)
