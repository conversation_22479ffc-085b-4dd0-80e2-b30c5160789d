# Enhanced Image Two-Stage Model

基于R2Gen的可插拔特征提取器Pipeline，支持多种视觉backbone的对比实验。

## 目录结构

```
enhanced_image_two_stage_model/
├── extractors/                     # 可插拔特征提取器模块
│   ├── base_extractor.py          # 抽象基类
│   └── r2gen_extractor.py         # R2Gen原始提取器
├── models/                         # 模型定义
├── modules/                        # 核心功能模块
├── scripts/                        # 训练和评估脚本
├── configs/                        # 配置文件
├── features/                       # 特征缓存目录
└── results/                        # 实验结果
```

## 使用方法

### 1. 特征提取
```bash
python scripts/extract_features.py --extractor configs/extractors/r2gen.yaml
```

### 2. 训练模型
```bash
python scripts/train_model.py --features_dir features/r2gen_baseline/
```

### 3. 评估模型
```bash
python scripts/evaluate_model.py --model_path results/model_best.pth
```

## 设计原则

1. **R2Gen Baseline优先**: 确保与原始R2Gen 100%一致性
2. **特征格式统一**: 所有提取器输出相同格式的特征
3. **可插拔设计**: 轻松切换不同的视觉backbone
4. **实验可重现**: 固定随机种子，确保结果一致性
