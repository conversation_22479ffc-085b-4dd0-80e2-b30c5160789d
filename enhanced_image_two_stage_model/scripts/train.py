#!/usr/bin/env python3
"""
二阶段训练主脚本

运行完整的二阶段训练流程：
1. 第一阶段：提取并保存视觉特征
2. 第二阶段：从特征训练到报告生成
"""

import os
import sys
import argparse
import json
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from modules.trainer import TwoStageTrainer


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='R2Gen 二阶段训练')
    
    # 数据相关参数
    parser.add_argument('--dataset_name', type=str, default='iu_xray',
                       help='数据集名称')
    parser.add_argument('--image_dir', type=str, 
                       default='../R2Gen-main/data/iu_xray/images',
                       help='图像目录路径')
    parser.add_argument('--ann_path', type=str, 
                       default='../R2Gen-main/data/iu_xray/annotation.json',
                       help='标注文件路径')
    parser.add_argument('--features_dir', type=str, default='features',
                       help='特征保存目录')
    parser.add_argument('--save_dir', type=str, default='checkpoints',
                       help='模型保存目录')
    
    # 模型相关参数
    parser.add_argument('--d_model', type=int, default=512,
                       help='模型维度')
    parser.add_argument('--d_ff', type=int, default=512,
                       help='前馈网络维度')
    parser.add_argument('--num_layers', type=int, default=3,
                       help='Transformer层数')
    parser.add_argument('--num_heads', type=int, default=8,
                       help='注意力头数')
    parser.add_argument('--dropout', type=float, default=0.1,
                       help='Dropout率')
    parser.add_argument('--max_seq_length', type=int, default=60,
                       help='最大序列长度')
    parser.add_argument('--threshold', type=int, default=3,
                       help='词汇表阈值')
    
    # 记忆模块参数
    parser.add_argument('--rm_num_slots', type=int, default=3,
                       help='关系记忆槽数')
    parser.add_argument('--rm_num_heads', type=int, default=8,
                       help='关系记忆头数')
    parser.add_argument('--rm_d_model', type=int, default=512,
                       help='关系记忆模型维度')
    
    # 训练相关参数
    parser.add_argument('--batch_size', type=int, default=16,
                       help='批次大小')
    parser.add_argument('--epochs', type=int, default=5,
                       help='训练轮数')
    parser.add_argument('--lr', type=float, default=1e-4,
                       help='学习率')
    parser.add_argument('--weight_decay', type=float, default=5e-5,
                       help='权重衰减')
    parser.add_argument('--step_size', type=int, default=50,
                       help='学习率衰减步长')
    parser.add_argument('--gamma', type=float, default=0.1,
                       help='学习率衰减因子')
    parser.add_argument('--grad_clip', type=float, default=5.0,
                       help='梯度裁剪阈值')
    parser.add_argument('--num_workers', type=int, default=4,
                       help='数据加载工作进程数')
    
    # 其他参数
    parser.add_argument('--pretrained_path', type=str, default=None,
                       help='预训练模型路径')
    parser.add_argument('--seed', type=int, default=42,
                       help='随机种子')
    
    return parser.parse_args()


def setup_environment(args):
    """设置训练环境"""
    import torch
    import numpy as np
    import random
    
    # 设置随机种子
    torch.manual_seed(args.seed)
    torch.cuda.manual_seed(args.seed)
    torch.cuda.manual_seed_all(args.seed)
    np.random.seed(args.seed)
    random.seed(args.seed)
    
    # 设置CUDA
    if torch.cuda.is_available():
        torch.backends.cudnn.deterministic = True
        torch.backends.cudnn.benchmark = False
        print(f"使用GPU: {torch.cuda.get_device_name()}")
    else:
        print("使用CPU")
    
    # 创建保存目录
    os.makedirs(args.save_dir, exist_ok=True)
    os.makedirs(args.features_dir, exist_ok=True)


def main():
    """主函数"""
    print("🚀 启动R2Gen二阶段训练")
    
    # 解析参数
    args = parse_arguments()
    
    # 设置环境
    setup_environment(args)
    
    # 保存配置
    config_file = os.path.join(args.save_dir, 'config.json')
    with open(config_file, 'w') as f:
        json.dump(vars(args), f, indent=2)
    print(f"配置已保存到: {config_file}")
    
    # 打印关键配置
    print(f"\n📋 训练配置:")
    print(f"  - 数据集: {args.dataset_name}")
    print(f"  - 训练轮数: {args.epochs}")
    print(f"  - 批次大小: {args.batch_size}")
    print(f"  - 学习率: {args.lr}")
    print(f"  - 模型维度: {args.d_model}")
    print(f"  - 层数: {args.num_layers}")
    
    # 创建训练器配置
    trainer_config = vars(args)
    
    try:
        # 初始化训练器
        trainer = TwoStageTrainer(trainer_config)
        
        # 运行训练
        results = trainer.run_full_training()
        
        print(f"\n✅ 训练成功完成!")
        print(f"最佳验证BLEU-4: {results['best_val_bleu4']:.4f}")
        
        return results
        
    except Exception as e:
        print(f"\n❌ 训练过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == '__main__':
    results = main()
    
    if results is not None:
        print("\n🎉 二阶段训练完成!")
    else:
        print("\n💥 训练失败!")
        sys.exit(1)
