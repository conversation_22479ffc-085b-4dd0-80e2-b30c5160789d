"""
特征数据集加载器

用于加载预计算的特征文件，支持二阶段训练。
"""

import os
import json
import torch
import numpy as np
from torch.utils.data import Dataset, DataLoader
from typing import Dict, List, Tuple, Any


class FeatureDataset(Dataset):
    """
    特征数据集
    
    加载预计算的视觉特征和对应的文本标注，用于二阶段训练。
    """
    
    def __init__(self, features_dir: str, ann_path: str, tokenizer, split: str = 'train'):
        """
        初始化特征数据集
        
        Args:
            features_dir: 特征文件目录
            ann_path: 标注文件路径
            tokenizer: 分词器
            split: 数据集分割 ('train', 'val', 'test')
        """
        self.features_dir = features_dir
        self.ann_path = ann_path
        self.tokenizer = tokenizer
        self.split = split
        
        # 加载标注数据
        self.annotations = self._load_annotations()
        
        # 过滤出当前分割的数据
        self.data = [item for item in self.annotations if item['split'] == split]
        
        print(f"加载 {split} 数据集:")
        print(f"  - 样本数量: {len(self.data)}")
        print(f"  - 特征目录: {features_dir}")
        
    def _load_annotations(self) -> List[Dict]:
        """
        加载标注文件
        
        Returns:
            List[Dict]: 标注数据列表
        """
        with open(self.ann_path, 'r') as f:
            annotations = json.load(f)
        
        # 确保每个样本都有必要的字段
        for item in annotations:
            if 'split' not in item:
                raise ValueError(f"标注数据缺少 'split' 字段: {item}")
            if 'id' not in item:
                raise ValueError(f"标注数据缺少 'id' 字段: {item}")
            if 'report' not in item:
                raise ValueError(f"标注数据缺少 'report' 字段: {item}")
                
        return annotations
    
    def _load_features(self, sample_id: str) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        加载预计算的特征
        
        Args:
            sample_id: 样本ID
            
        Returns:
            Tuple[torch.Tensor, torch.Tensor]: (att_feats, fc_feats)
        """
        feature_file = os.path.join(self.features_dir, f"{sample_id}.npz")
        
        if not os.path.exists(feature_file):
            raise FileNotFoundError(f"特征文件不存在: {feature_file}")
        
        # 加载特征
        features = np.load(feature_file)
        
        # 检查必要的键
        if 'att_feats' not in features or 'fc_feats' not in features:
            raise ValueError(f"特征文件格式错误: {feature_file}")
        
        att_feats = torch.from_numpy(features['att_feats']).float()  # (98, 2048)
        fc_feats = torch.from_numpy(features['fc_feats']).float()    # (4096,)
        
        return att_feats, fc_feats
    
    def _tokenize_report(self, report: str) -> Tuple[List[int], List[int]]:
        """
        对报告进行分词
        
        Args:
            report: 报告文本
            
        Returns:
            Tuple[List[int], List[int]]: (token_ids, mask)
        """
        # 使用分词器处理报告
        tokens = self.tokenizer(report)
        
        # 创建mask（1表示有效token，0表示padding）
        mask = [1] * len(tokens)
        
        return tokens, mask
    
    def __len__(self) -> int:
        """返回数据集大小"""
        return len(self.data)
    
    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        """
        获取单个样本
        
        Args:
            idx: 样本索引
            
        Returns:
            Dict[str, torch.Tensor]: 样本数据
                - 'att_feats': 注意力特征 (98, 2048)
                - 'fc_feats': 全连接特征 (4096,)
                - 'labels': 标签序列 (seq_len,)
                - 'masks': 掩码序列 (seq_len,)
                - 'sample_id': 样本ID
        """
        item = self.data[idx]
        sample_id = item['id']
        report = item['report']
        
        # 加载特征
        att_feats, fc_feats = self._load_features(sample_id)
        
        # 分词
        token_ids, mask = self._tokenize_report(report)
        
        return {
            'att_feats': att_feats,
            'fc_feats': fc_feats,
            'labels': torch.LongTensor(token_ids),
            'masks': torch.FloatTensor(mask),
            'sample_id': sample_id
        }


def collate_fn(batch: List[Dict[str, torch.Tensor]]) -> Dict[str, torch.Tensor]:
    """
    批次整理函数
    
    Args:
        batch: 批次数据列表
        
    Returns:
        Dict[str, torch.Tensor]: 整理后的批次数据
    """
    # 获取批次大小
    batch_size = len(batch)
    
    # 收集所有特征
    att_feats = torch.stack([item['att_feats'] for item in batch])  # (B, 98, 2048)
    fc_feats = torch.stack([item['fc_feats'] for item in batch])    # (B, 4096)
    
    # 处理变长序列
    max_seq_len = max(len(item['labels']) for item in batch)
    
    # 初始化填充后的张量
    labels = torch.zeros(batch_size, max_seq_len, dtype=torch.long)
    masks = torch.zeros(batch_size, max_seq_len, dtype=torch.float)
    
    # 填充序列
    for i, item in enumerate(batch):
        seq_len = len(item['labels'])
        labels[i, :seq_len] = item['labels']
        masks[i, :seq_len] = item['masks']
    
    # 收集样本ID
    sample_ids = [item['sample_id'] for item in batch]
    
    return {
        'att_feats': att_feats,
        'fc_feats': fc_feats,
        'labels': labels,
        'masks': masks,
        'sample_ids': sample_ids
    }


def create_feature_dataloader(features_dir: str, ann_path: str, tokenizer, 
                             split: str, batch_size: int = 16, 
                             shuffle: bool = True, num_workers: int = 4) -> DataLoader:
    """
    创建特征数据加载器
    
    Args:
        features_dir: 特征文件目录
        ann_path: 标注文件路径
        tokenizer: 分词器
        split: 数据集分割
        batch_size: 批次大小
        shuffle: 是否打乱
        num_workers: 工作进程数
        
    Returns:
        DataLoader: 数据加载器
    """
    dataset = FeatureDataset(features_dir, ann_path, tokenizer, split)
    
    dataloader = DataLoader(
        dataset,
        batch_size=batch_size,
        shuffle=shuffle,
        collate_fn=collate_fn,
        num_workers=num_workers,
        pin_memory=True
    )
    
    return dataloader
