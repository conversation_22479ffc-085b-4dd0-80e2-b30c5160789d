#!/usr/bin/env python3
"""
简化版二阶段训练脚本

直接使用现有的two_stage_model进行训练，避免复杂的导入问题。
"""

import os
import sys
import time
import json
import argparse
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))
sys.path.append(str(project_root / 'R2Gen-main'))
sys.path.append(str(project_root / 'two_stage_model'))

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='R2Gen 简化版二阶段训练')
    
    parser.add_argument('--epochs', type=int, default=5, help='训练轮数')
    parser.add_argument('--batch_size', type=int, default=8, help='批次大小')
    parser.add_argument('--lr', type=float, default=1e-4, help='学习率')
    
    return parser.parse_args()

def main():
    """主函数"""
    print("🚀 启动R2Gen简化版二阶段训练")
    
    # 解析参数
    args = parse_arguments()
    
    print(f"\n📋 训练配置:")
    print(f"  - 训练轮数: {args.epochs}")
    print(f"  - 批次大小: {args.batch_size}")
    print(f"  - 学习率: {args.lr}")
    
    try:
        # 导入现有的训练器
        from stage2_trainer import Stage2Trainer
        
        # 创建训练参数
        class TrainingArgs:
            # 数据相关
            features_dir = str(project_root / 'two_stage_model' / 'features')
            ann_path = str(project_root / 'R2Gen-main' / 'data' / 'iu_xray' / 'annotation.json')
            dataset_name = 'iu_xray'
            
            # 模型参数
            max_seq_length = 60
            threshold = 3
            num_layers = 3
            d_model = 512
            d_ff = 512
            num_heads = 8
            dropout = 0.1
            
            # 记忆模块参数
            rm_num_slots = 3
            rm_num_heads = 8
            rm_d_model = 512
            
            # 训练参数
            batch_size = args.batch_size
            epochs = args.epochs
            lr_ed = args.lr
            weight_decay = 5e-5
            step_size = 50
            gamma = 0.1
            grad_clip = 5.0
            
            # 其他参数
            logit_layers = 1
            bos_idx = 0
            eos_idx = 0
            pad_idx = 0
            use_bn = 0
            drop_prob_lm = 0.5
            sample_method = 'beam_search'
            
            # 保存目录
            save_dir = str(project_root / 'enhanced_image_two_stage_model' / 'checkpoints')
            
        training_args = TrainingArgs()
        
        # 创建保存目录
        os.makedirs(training_args.save_dir, exist_ok=True)
        
        # 记录开始时间
        start_time = time.time()
        
        print(f"\n初始化训练器...")
        trainer = Stage2Trainer(training_args)
        
        print(f"开始训练...")
        test_scores = trainer.train()
        
        # 记录结束时间
        end_time = time.time()
        training_time = end_time - start_time
        
        print(f"\n=== 训练完成 ===")
        print(f"总训练时间: {training_time:.2f} 秒")
        print(f"平均每轮时间: {training_time / args.epochs:.2f} 秒")
        
        # 保存最终结果
        results = {
            'training_time': training_time,
            'epochs': args.epochs,
            'best_val_bleu4': trainer.best_val_score,
            'test_scores': test_scores,
            'train_losses': trainer.train_losses,
            'val_scores': trainer.val_scores,
            'config': {
                'epochs': args.epochs,
                'batch_size': args.batch_size,
                'lr': args.lr,
                'model': 'R2Gen_enhanced_two_stage'
            }
        }
        
        results_file = os.path.join(training_args.save_dir, 'training_results.json')
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"✅ 结果已保存到: {results_file}")
        
        # 更新对比文件
        update_comparison_file(results)
        
        return results
        
    except Exception as e:
        print(f"\n❌ 训练过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def update_comparison_file(results):
    """更新模型对比文件"""
    try:
        comparison_file = project_root / 'iu_xray_model_comparison.md'
        
        # 读取现有内容
        if comparison_file.exists():
            with open(comparison_file, 'r', encoding='utf-8') as f:
                content = f.read()
        else:
            content = "# IU X-ray数据集模型性能对比\n\n"
        
        # 添加新结果
        new_entry = f"""
## R2Gen Enhanced Two-Stage (训练时间: {time.strftime('%Y-%m-%d %H:%M:%S')})

**配置:**
- 模型: R2Gen Enhanced Two-Stage
- 训练轮数: {results['epochs']}
- 批次大小: {results['config']['batch_size']}
- 学习率: {results['config']['lr']}
- 训练时间: {results['training_time']:.2f} 秒

**性能指标:**
- 最佳验证BLEU-4: {results['best_val_bleu4']:.4f}

**测试集结果:**
"""
        
        if results['test_scores']:
            for metric, score in results['test_scores'].items():
                new_entry += f"- {metric}: {score:.4f}\n"
        
        new_entry += "\n---\n"
        
        # 写入文件
        with open(comparison_file, 'a', encoding='utf-8') as f:
            f.write(new_entry)
        
        print(f"✅ 结果已添加到对比文件: {comparison_file}")
        
    except Exception as e:
        print(f"⚠️ 更新对比文件失败: {e}")

if __name__ == '__main__':
    results = main()
    
    if results is not None:
        print("\n🎉 二阶段训练完成!")
        print(f"最佳验证BLEU-4: {results['best_val_bleu4']:.4f}")
    else:
        print("\n💥 训练失败!")
        sys.exit(1)
