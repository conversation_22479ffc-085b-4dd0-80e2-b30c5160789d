{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 10 - 结果可视化与分析\n", "\n", "在这个notebook中，我们将学习如何可视化和分析医学报告生成模型的结果，包括训练过程监控、模型行为分析、注意力可视化等。\n", "\n", "## 学习目标\n", "\n", "- 掌握训练过程的可视化方法\n", "- 学习模型行为分析技术\n", "- 实现注意力机制可视化\n", "- 进行错误分析和案例研究\n", "- 创建交互式分析工具\n", "- 生成专业的分析报告\n", "- 理解模型的优势和局限性\n", "\n", "## 可视化的重要性\n", "\n", "在深度学习项目中，可视化分析具有重要意义：\n", "\n", "1. **理解模型行为**：通过可视化了解模型的工作机制\n", "2. **发现问题**：及时发现训练过程中的异常\n", "3. **指导改进**：为模型优化提供方向\n", "4. **结果展示**：向利益相关者展示成果\n", "5. **知识传递**：帮助团队成员理解模型\n", "\n", "### 医学报告生成的特殊需求\n", "\n", "医学报告生成的可视化分析还需要考虑：\n", "- **临床相关性**：分析结果的临床意义\n", "- **安全性评估**：识别潜在的医学错误\n", "- **可解释性**：提供决策过程的透明度\n", "- **专业验证**：支持医学专家的审查"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 导入必要的库\n", "import os\n", "import sys\n", "import json\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import plotly.graph_objects as go\n", "import plotly.express as px\n", "from plotly.subplots import make_subplots\n", "import plotly.figure_factory as ff\n", "from wordcloud import WordCloud\n", "import torch\n", "import torch.nn.functional as F\n", "from sklearn.manifold import TSNE\n", "from sklearn.decomposition import PCA\n", "from sklearn.cluster import KMeans\n", "from collections import Counter, defaultdict\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# 设置项目路径\n", "project_root = os.path.abspath('..')\n", "sys.path.append(project_root)\n", "\n", "print(f\"项目根目录: {project_root}\")\n", "\n", "# 设置绘图样式\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "\n", "# 设置中文字体（如果需要）\n", "plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "# 设置随机种子\n", "np.random.seed(42)\n", "torch.manual_seed(42)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 训练过程可视化\n", "\n", "首先，我们实现训练过程的详细可视化："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class TrainingVisualizer:\n", "    \"\"\"\n", "    训练过程可视化器\n", "    \n", "    提供训练过程的各种可视化功能\n", "    \"\"\"\n", "    \n", "    def __init__(self):\n", "        self.colors = px.colors.qualitative.Set1\n", "    \n", "    def plot_training_curves(self, history, save_path=None):\n", "        \"\"\"\n", "        绘制训练曲线\n", "        \n", "        Args:\n", "            history (dict): 训练历史数据\n", "            save_path (str): 保存路径\n", "        \"\"\"\n", "        fig = make_subplots(\n", "            rows=2, cols=2,\n", "            subplot_titles=('损失曲线', '准确率曲线', '学习率变化', '梯度范数'),\n", "            specs=[[{\"secondary_y\": False}, {\"secondary_y\": False}],\n", "                   [{\"secondary_y\": False}, {\"secondary_y\": False}]]\n", "        )\n", "        \n", "        epochs = list(range(1, len(history['train_loss']) + 1))\n", "        \n", "        # 损失曲线\n", "        fig.add_trace(\n", "            go.<PERSON>er(x=epochs, y=history['train_loss'], \n", "                      name='训练损失', line=dict(color=self.colors[0])),\n", "            row=1, col=1\n", "        )\n", "        fig.add_trace(\n", "            go.<PERSON><PERSON>(x=epochs, y=history['val_loss'], \n", "                      name='验证损失', line=dict(color=self.colors[1])),\n", "            row=1, col=1\n", "        )\n", "        \n", "        # 准确率曲线\n", "        fig.add_trace(\n", "            go.<PERSON>er(x=epochs, y=history['train_acc'], \n", "                      name='训练准确率', line=dict(color=self.colors[2])),\n", "            row=1, col=2\n", "        )\n", "        fig.add_trace(\n", "            go.<PERSON>er(x=epochs, y=history['val_acc'], \n", "                      name='验证准确率', line=dict(color=self.colors[3])),\n", "            row=1, col=2\n", "        )\n", "        \n", "        # 学习率变化\n", "        fig.add_trace(\n", "            go.<PERSON>er(x=epochs, y=history['learning_rates'], \n", "                      name='学习率', line=dict(color=self.colors[4])),\n", "            row=2, col=1\n", "        )\n", "        \n", "        # 梯度范数（如果有）\n", "        if 'grad_norms' in history and history['grad_norms']:\n", "            batch_steps = list(range(len(history['grad_norms'])))\n", "            fig.add_trace(\n", "                go.<PERSON>er(x=batch_steps, y=history['grad_norms'], \n", "                          name='梯度范数', line=dict(color=self.colors[5])),\n", "                row=2, col=2\n", "            )\n", "        \n", "        # 更新布局\n", "        fig.update_layout(\n", "            title='训练过程监控',\n", "            height=800,\n", "            showlegend=True\n", "        )\n", "        \n", "        # 更新坐标轴标签\n", "        fig.update_xaxes(title_text=\"Epoch\", row=1, col=1)\n", "        fig.update_xaxes(title_text=\"Epoch\", row=1, col=2)\n", "        fig.update_xaxes(title_text=\"Epoch\", row=2, col=1)\n", "        fig.update_xaxes(title_text=\"Batch\", row=2, col=2)\n", "        \n", "        fig.update_yaxes(title_text=\"Loss\", row=1, col=1)\n", "        fig.update_yaxes(title_text=\"Accuracy\", row=1, col=2)\n", "        fig.update_yaxes(title_text=\"Learning Rate\", row=2, col=1)\n", "        fig.update_yaxes(title_text=\"Gradient Norm\", row=2, col=2)\n", "        \n", "        if save_path:\n", "            fig.write_html(save_path)\n", "            print(f\"训练曲线已保存到: {save_path}\")\n", "        \n", "        fig.show()\n", "        return fig\n", "    \n", "    def plot_loss_landscape(self, loss_values, save_path=None):\n", "        \"\"\"\n", "        绘制损失地形图\n", "        \n", "        Args:\n", "            loss_values (np.ndarray): 损失值矩阵\n", "            save_path (str): 保存路径\n", "        \"\"\"\n", "        fig = go.Figure(data=go.Heatmap(\n", "            z=loss_values,\n", "            colorscale='Viridis',\n", "            colorbar=dict(title=\"Loss Value\")\n", "        ))\n", "        \n", "        fig.update_layout(\n", "            title='损失函数地形图',\n", "            xaxis_title='参数维度1',\n", "            yaxis_title='参数维度2'\n", "        )\n", "        \n", "        if save_path:\n", "            fig.write_html(save_path)\n", "        \n", "        fig.show()\n", "        return fig\n", "\n", "# 创建示例训练历史数据\n", "def create_sample_training_history():\n", "    \"\"\"\n", "    创建示例训练历史数据用于演示\n", "    \"\"\"\n", "    epochs = 50\n", "    \n", "    # 模拟训练过程\n", "    train_loss = []\n", "    val_loss = []\n", "    train_acc = []\n", "    val_acc = []\n", "    learning_rates = []\n", "    \n", "    for epoch in range(epochs):\n", "        # 模拟损失下降\n", "        train_l = 2.0 * np.exp(-epoch * 0.1) + 0.1 + 0.05 * np.random.randn()\n", "        val_l = train_l + 0.1 + 0.02 * np.random.randn()\n", "        \n", "        # 模拟准确率上升\n", "        train_a = 1 - np.exp(-epoch * 0.08) + 0.02 * np.random.randn()\n", "        val_a = train_a - 0.05 + 0.02 * np.random.randn()\n", "        \n", "        # 模拟学习率衰减\n", "        lr = 0.001 * (0.95 ** epoch)\n", "        \n", "        train_loss.append(max(0.01, train_l))\n", "        val_loss.append(max(0.01, val_l))\n", "        train_acc.append(min(0.99, max(0.01, train_a)))\n", "        val_acc.append(min(0.99, max(0.01, val_a)))\n", "        learning_rates.append(lr)\n", "    \n", "    # 模拟梯度范数\n", "    grad_norms = []\n", "    for i in range(epochs * 20):  # 假设每个epoch有20个batch\n", "        grad_norm = 1.0 + 0.5 * np.sin(i * 0.1) + 0.2 * np.random.randn()\n", "        grad_norms.append(max(0.1, grad_norm))\n", "    \n", "    return {\n", "        'train_loss': train_loss,\n", "        'val_loss': val_loss,\n", "        'train_acc': train_acc,\n", "        'val_acc': val_acc,\n", "        'learning_rates': learning_rates,\n", "        'grad_norms': grad_norms\n", "    }\n", "\n", "# 演示训练过程可视化\n", "print(\"=== 训练过程可视化演示 ===\")\n", "sample_history = create_sample_training_history()\n", "visualizer = TrainingVisualizer()\n", "training_fig = visualizer.plot_training_curves(sample_history)\n", "print(\"训练曲线可视化完成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 注意力机制可视化\n", "\n", "注意力可视化是理解Transformer模型行为的重要工具："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class AttentionVisualizer:\n", "    \"\"\"\n", "    注意力机制可视化器\n", "    \n", "    提供多种注意力可视化方法\n", "    \"\"\"\n", "    \n", "    def __init__(self):\n", "        pass\n", "    \n", "    def plot_attention_heatmap(self, attention_weights, source_tokens, target_tokens, \n", "                              title=\"注意力热图\", save_path=None):\n", "        \"\"\"\n", "        绘制注意力热图\n", "        \n", "        Args:\n", "            attention_weights (np.ndarray): 注意力权重矩阵 (target_len, source_len)\n", "            source_tokens (list): 源序列token列表\n", "            target_tokens (list): 目标序列token列表\n", "            title (str): 图表标题\n", "            save_path (str): 保存路径\n", "        \"\"\"\n", "        fig = go.Figure(data=go.Heatmap(\n", "            z=attention_weights,\n", "            x=source_tokens,\n", "            y=target_tokens,\n", "            colorscale='Blues',\n", "            showscale=True,\n", "            colorbar=dict(title=\"注意力权重\")\n", "        ))\n", "        \n", "        fig.update_layout(\n", "            title=title,\n", "            xaxis_title=\"源序列\",\n", "            yaxis_title=\"目标序列\",\n", "            width=800,\n", "            height=600\n", "        )\n", "        \n", "        # 调整字体大小以适应token显示\n", "        fig.update_xaxes(tickangle=45)\n", "        fig.update_yaxes(tickangle=0)\n", "        \n", "        if save_path:\n", "            fig.write_html(save_path)\n", "        \n", "        fig.show()\n", "        return fig\n", "    \n", "    def plot_multi_head_attention(self, attention_weights, source_tokens, target_tokens, \n", "                                 num_heads=8, save_path=None):\n", "        \"\"\"\n", "        绘制多头注意力\n", "        \n", "        Args:\n", "            attention_weights (np.ndarray): 多头注意力权重 (num_heads, target_len, source_len)\n", "            source_tokens (list): 源序列token列表\n", "            target_tokens (list): 目标序列token列表\n", "            num_heads (int): 注意力头数\n", "            save_path (str): 保存路径\n", "        \"\"\"\n", "        # 计算子图布局\n", "        cols = min(4, num_heads)\n", "        rows = (num_heads + cols - 1) // cols\n", "        \n", "        fig = make_subplots(\n", "            rows=rows, cols=cols,\n", "            subplot_titles=[f'注意力头 {i+1}' for i in range(num_heads)],\n", "            vertical_spacing=0.1,\n", "            horizontal_spacing=0.1\n", "        )\n", "        \n", "        for head in range(num_heads):\n", "            row = head // cols + 1\n", "            col = head % cols + 1\n", "            \n", "            fig.add_trace(\n", "                go.Heatmap(\n", "                    z=attention_weights[head],\n", "                    x=source_tokens,\n", "                    y=target_tokens,\n", "                    colorscale='Blues',\n", "                    showscale=False\n", "                ),\n", "                row=row, col=col\n", "            )\n", "        \n", "        fig.update_layout(\n", "            title='多头注意力可视化',\n", "            height=200 * rows,\n", "            width=200 * cols\n", "        )\n", "        \n", "        if save_path:\n", "            fig.write_html(save_path)\n", "        \n", "        fig.show()\n", "        return fig\n", "    \n", "    def plot_attention_flow(self, attention_weights, tokens, layer_names, save_path=None):\n", "        \"\"\"\n", "        绘制注意力流动图\n", "        \n", "        Args:\n", "            attention_weights (list): 各层注意力权重列表\n", "            tokens (list): token列表\n", "            layer_names (list): 层名称列表\n", "            save_path (str): 保存路径\n", "        \"\"\"\n", "        num_layers = len(attention_weights)\n", "        \n", "        fig = make_subplots(\n", "            rows=1, cols=num_layers,\n", "            subplot_titles=layer_names,\n", "            horizontal_spacing=0.05\n", "        )\n", "        \n", "        for i, (weights, layer_name) in enumerate(zip(attention_weights, layer_names)):\n", "            fig.add_trace(\n", "                go.Heatmap(\n", "                    z=weights,\n", "                    x=tokens,\n", "                    y=tokens,\n", "                    colorscale='Viridis',\n", "                    showscale=(i == num_layers - 1)\n", "                ),\n", "                row=1, col=i+1\n", "            )\n", "        \n", "        fig.update_layout(\n", "            title='跨层注意力流动',\n", "            height=400,\n", "            width=300 * num_layers\n", "        )\n", "        \n", "        if save_path:\n", "            fig.write_html(save_path)\n", "        \n", "        fig.show()\n", "        return fig\n", "\n", "# 创建示例注意力数据\n", "def create_sample_attention_data():\n", "    \"\"\"\n", "    创建示例注意力数据用于演示\n", "    \"\"\"\n", "    # 示例医学报告tokens\n", "    source_tokens = ['chest', 'x-ray', 'shows', 'clear', 'lungs', 'normal', 'heart']\n", "    target_tokens = ['the', 'patient', 'has', 'clear', 'lungs', 'and', 'normal', 'heart']\n", "    \n", "    # 创建模拟注意力权重\n", "    np.random.seed(42)\n", "    \n", "    # 单头注意力\n", "    attention_single = np.random.rand(len(target_tokens), len(source_tokens))\n", "    # 归一化\n", "    attention_single = attention_single / attention_single.sum(axis=1, keepdims=True)\n", "    \n", "    # 多头注意力\n", "    num_heads = 8\n", "    attention_multi = np.random.rand(num_heads, len(target_tokens), len(source_tokens))\n", "    # 归一化每个头\n", "    for head in range(num_heads):\n", "        attention_multi[head] = attention_multi[head] / attention_multi[head].sum(axis=1, keepdims=True)\n", "    \n", "    # 多层注意力\n", "    num_layers = 6\n", "    attention_layers = []\n", "    for layer in range(num_layers):\n", "        layer_attention = np.random.rand(len(target_tokens), len(target_tokens))\n", "        layer_attention = layer_attention / layer_attention.sum(axis=1, keepdims=True)\n", "        attention_layers.append(layer_attention)\n", "    \n", "    return {\n", "        'source_tokens': source_tokens,\n", "        'target_tokens': target_tokens,\n", "        'attention_single': attention_single,\n", "        'attention_multi': attention_multi,\n", "        'attention_layers': attention_layers\n", "    }\n", "\n", "# 演示注意力可视化\n", "print(\"\\n=== 注意力机制可视化演示 ===\")\n", "attention_data = create_sample_attention_data()\n", "attention_viz = AttentionVisualizer()\n", "\n", "# 单头注意力热图\n", "single_attention_fig = attention_viz.plot_attention_heatmap(\n", "    attention_data['attention_single'],\n", "    attention_data['source_tokens'],\n", "    attention_data['target_tokens'],\n", "    \"编码器-解码器注意力\"\n", ")\n", "\n", "print(\"注意力可视化完成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 错误分析和案例研究\n", "\n", "深入分析模型的错误模式对改进模型至关重要："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class ErrorAnalyzer:\n", "    \"\"\"\n", "    错误分析器\n", "    \n", "    分析模型的错误模式和改进方向\n", "    \"\"\"\n", "    \n", "    def __init__(self):\n", "        self.error_categories = {\n", "            'medical_term_error': '医学术语错误',\n", "            'anatomical_error': '解剖结构错误',\n", "            'finding_error': '发现描述错误',\n", "            'severity_error': '严重程度错误',\n", "            'grammar_error': '语法错误',\n", "            'repetition_error': '重复错误',\n", "            'omission_error': '遗漏错误',\n", "            'hallucination_error': '幻觉错误'\n", "        }\n", "    \n", "    def analyze_prediction_errors(self, predictions, references, detailed=True):\n", "        \"\"\"\n", "        分析预测错误\n", "        \n", "        Args:\n", "            predictions (list): 预测结果列表\n", "            references (list): 参考答案列表\n", "            detailed (bool): 是否进行详细分析\n", "        \n", "        Returns:\n", "            dict: 错误分析结果\n", "        \"\"\"\n", "        error_analysis = {\n", "            'total_samples': len(predictions),\n", "            'error_categories': {cat: 0 for cat in self.error_categories},\n", "            'error_examples': [],\n", "            'length_analysis': {},\n", "            'word_level_errors': {}\n", "        }\n", "        \n", "        for i, (pred, ref) in enumerate(zip(predictions, references)):\n", "            # 基础错误检测\n", "            errors = self._detect_errors(pred, ref)\n", "            \n", "            for error_type in errors:\n", "                error_analysis['error_categories'][error_type] += 1\n", "            \n", "            # 收集错误示例\n", "            if errors and len(error_analysis['error_examples']) < 10:\n", "                error_analysis['error_examples'].append({\n", "                    'index': i,\n", "                    'prediction': pred,\n", "                    'reference': ref,\n", "                    'errors': errors\n", "                })\n", "        \n", "        # 长度分析\n", "        pred_lengths = [len(pred.split()) for pred in predictions]\n", "        ref_lengths = [len(ref.split()) for ref in references]\n", "        \n", "        error_analysis['length_analysis'] = {\n", "            'avg_pred_length': np.mean(pred_lengths),\n", "            'avg_ref_length': np.mean(ref_lengths),\n", "            'length_correlation': np.corrcoef(pred_lengths, ref_lengths)[0, 1]\n", "        }\n", "        \n", "        return error_analysis\n", "    \n", "    def _detect_errors(self, prediction, reference):\n", "        \"\"\"\n", "        检测单个样本的错误类型\n", "        \n", "        Args:\n", "            prediction (str): 预测文本\n", "            reference (str): 参考文本\n", "        \n", "        Returns:\n", "            list: 错误类型列表\n", "        \"\"\"\n", "        errors = []\n", "        \n", "        pred_words = prediction.lower().split()\n", "        ref_words = reference.lower().split()\n", "        \n", "        # 检测重复错误\n", "        if self._has_repetition(pred_words):\n", "            errors.append('repetition_error')\n", "        \n", "        # 检测遗漏错误（简化版）\n", "        medical_terms = ['pneumonia', 'fracture', 'normal', 'clear', 'abnormal']\n", "        ref_medical = [term for term in medical_terms if term in ref_words]\n", "        pred_medical = [term for term in medical_terms if term in pred_words]\n", "        \n", "        if len(pred_medical) < len(ref_medical):\n", "            errors.append('omission_error')\n", "        \n", "        # 检测幻觉错误（预测中有但参考中没有的医学术语）\n", "        if len(pred_medical) > len(ref_medical):\n", "            errors.append('hallucination_error')\n", "        \n", "        # 检测长度异常\n", "        if abs(len(pred_words) - len(ref_words)) > len(ref_words) * 0.5:\n", "            errors.append('length_error')\n", "        \n", "        return errors\n", "    \n", "    def _has_repetition(self, words):\n", "        \"\"\"\n", "        检测是否有重复\n", "        \n", "        Args:\n", "            words (list): 单词列表\n", "        \n", "        Returns:\n", "            bool: 是否有重复\n", "        \"\"\"\n", "        # 检测连续重复的单词或短语\n", "        for i in range(len(words) - 1):\n", "            if words[i] == words[i + 1]:\n", "                return True\n", "        \n", "        # 检测重复的短语\n", "        for length in [2, 3]:\n", "            phrases = [' '.join(words[i:i+length]) for i in range(len(words)-length+1)]\n", "            if len(phrases) != len(set(phrases)):\n", "                return True\n", "        \n", "        return False\n", "    \n", "    def plot_error_distribution(self, error_analysis, save_path=None):\n", "        \"\"\"\n", "        绘制错误分布图\n", "        \n", "        Args:\n", "            error_analysis (dict): 错误分析结果\n", "            save_path (str): 保存路径\n", "        \"\"\"\n", "        categories = list(self.error_categories.keys())\n", "        counts = [error_analysis['error_categories'][cat] for cat in categories]\n", "        labels = [self.error_categories[cat] for cat in categories]\n", "        \n", "        # 过滤掉计数为0的类别\n", "        non_zero_data = [(label, count) for label, count in zip(labels, counts) if count > 0]\n", "        \n", "        if not non_zero_data:\n", "            print(\"没有检测到错误\")\n", "            return None\n", "        \n", "        labels, counts = zip(*non_zero_data)\n", "        \n", "        fig = go.Figure(data=[\n", "            go.Bar(x=list(labels), y=list(counts), \n", "                  marker_color=px.colors.qualitative.Set3[:len(labels)])\n", "        ])\n", "        \n", "        fig.update_layout(\n", "            title='错误类型分布',\n", "            xaxis_title='错误类型',\n", "            yaxis_title='错误数量',\n", "            xaxis_tickangle=-45\n", "        )\n", "        \n", "        if save_path:\n", "            fig.write_html(save_path)\n", "        \n", "        fig.show()\n", "        return fig\n", "\n", "# 创建示例预测数据\n", "def create_sample_predictions():\n", "    \"\"\"\n", "    创建示例预测数据用于错误分析\n", "    \"\"\"\n", "    predictions = [\n", "        \"The chest X-ray shows clear lungs with no evidence of pneumonia pneumonia.\",  # 重复错误\n", "        \"Normal chest radiograph.\",  # 遗漏错误\n", "        \"The patient has severe pneumonia with bilateral lung consolidation and pleural effusion.\",  # 幻觉错误\n", "        \"Chest X-ray demonstrates clear lung fields without any acute abnormalities.\",  # 正常\n", "        \"The heart size is normal and the lungs are clear clear clear.\",  # 重复错误\n", "    ]\n", "    \n", "    references = [\n", "        \"The chest X-ray shows clear lungs with no evidence of pneumonia.\",\n", "        \"Normal chest radiograph. No acute cardiopulmonary abnormalities.\",\n", "        \"The patient has clear lungs with no abnormalities.\",\n", "        \"Chest X-ray demonstrates clear lung fields without any acute abnormalities.\",\n", "        \"The heart size is normal and the lungs are clear.\",\n", "    ]\n", "    \n", "    return predictions, references\n", "\n", "# 演示错误分析\n", "print(\"\\n=== 错误分析演示 ===\")\n", "sample_predictions, sample_references = create_sample_predictions()\n", "error_analyzer = ErrorAnalyzer()\n", "\n", "error_analysis = error_analyzer.analyze_prediction_errors(\n", "    sample_predictions, sample_references\n", ")\n", "\n", "print(f\"总样本数: {error_analysis['total_samples']}\")\n", "print(\"错误类型统计:\")\n", "for error_type, count in error_analysis['error_categories'].items():\n", "    if count > 0:\n", "        print(f\"  {error_analyzer.error_categories[error_type]}: {count}\")\n", "\n", "# 绘制错误分布\n", "error_fig = error_analyzer.plot_error_distribution(error_analysis)\n", "print(\"错误分析完成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 词汇分析和词云可视化\n", "\n", "分析生成文本的词汇使用模式："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class VocabularyAnalyzer:\n", "    \"\"\"\n", "    词汇分析器\n", "    \n", "    分析生成文本的词汇使用模式\n", "    \"\"\"\n", "    \n", "    def __init__(self):\n", "        self.medical_categories = {\n", "            'anatomy': ['heart', 'lung', 'chest', 'liver', 'kidney', 'spine', 'brain'],\n", "            'findings': ['normal', 'abnormal', 'clear', 'opacity', 'mass', 'lesion'],\n", "            'severity': ['mild', 'moderate', 'severe', 'acute', 'chronic'],\n", "            'location': ['left', 'right', 'bilateral', 'upper', 'lower']\n", "        }\n", "    \n", "    def analyze_vocabulary_usage(self, texts, text_type=\"generated\"):\n", "        \"\"\"\n", "        分析词汇使用情况\n", "        \n", "        Args:\n", "            texts (list): 文本列表\n", "            text_type (str): 文本类型标识\n", "        \n", "        Returns:\n", "            dict: 词汇分析结果\n", "        \"\"\"\n", "        all_words = []\n", "        for text in texts:\n", "            words = text.lower().split()\n", "            all_words.extend(words)\n", "        \n", "        word_freq = Counter(all_words)\n", "        \n", "        # 医学词汇分析\n", "        medical_usage = {}\n", "        for category, terms in self.medical_categories.items():\n", "            category_count = sum(word_freq[term] for term in terms if term in word_freq)\n", "            medical_usage[category] = category_count\n", "        \n", "        # 词汇多样性\n", "        unique_words = len(set(all_words))\n", "        total_words = len(all_words)\n", "        vocabulary_diversity = unique_words / total_words if total_words > 0 else 0\n", "        \n", "        return {\n", "            'text_type': text_type,\n", "            'total_words': total_words,\n", "            'unique_words': unique_words,\n", "            'vocabulary_diversity': vocabulary_diversity,\n", "            'word_frequency': dict(word_freq.most_common(50)),\n", "            'medical_usage': medical_usage,\n", "            'avg_text_length': np.mean([len(text.split()) for text in texts])\n", "        }\n", "    \n", "    def create_wordcloud(self, texts, title=\"词云\", save_path=None):\n", "        \"\"\"\n", "        创建词云\n", "        \n", "        Args:\n", "            texts (list): 文本列表\n", "            title (str): 标题\n", "            save_path (str): 保存路径\n", "        \"\"\"\n", "        # 合并所有文本\n", "        combined_text = ' '.join(texts)\n", "        \n", "        try:\n", "            # 创建词云\n", "            wordcloud = WordCloud(\n", "                width=800, height=400,\n", "                background_color='white',\n", "                max_words=100,\n", "                colormap='viridis'\n", "            ).generate(combined_text)\n", "            \n", "            # 绘制词云\n", "            plt.figure(figsize=(12, 6))\n", "            plt.imshow(wordcloud, interpolation='bilinear')\n", "            plt.axis('off')\n", "            plt.title(title, fontsize=16, pad=20)\n", "            \n", "            if save_path:\n", "                plt.savefig(save_path, dpi=300, bbox_inches='tight')\n", "            \n", "            plt.show()\n", "            \n", "        except Exception as e:\n", "            print(f\"词云生成失败: {e}\")\n", "            print(\"使用条形图替代...\")\n", "            \n", "            # 备用方案：使用条形图\n", "            words = combined_text.split()\n", "            word_freq = Counter(words)\n", "            top_words = dict(word_freq.most_common(20))\n", "            \n", "            plt.figure(figsize=(12, 6))\n", "            plt.bar(range(len(top_words)), list(top_words.values()))\n", "            plt.xticks(range(len(top_words)), list(top_words.keys()), rotation=45)\n", "            plt.title(f\"{title} - 高频词汇\")\n", "            plt.ylabel('频次')\n", "            plt.tight_layout()\n", "            \n", "            if save_path:\n", "                plt.savefig(save_path, dpi=300, bbox_inches='tight')\n", "            \n", "            plt.show()\n", "    \n", "    def compare_vocabularies(self, texts1, texts2, labels=['文本1', '文本2']):\n", "        \"\"\"\n", "        比较两组文本的词汇使用\n", "        \n", "        Args:\n", "            texts1 (list): 第一组文本\n", "            texts2 (list): 第二组文本\n", "            labels (list): 标签列表\n", "        \n", "        Returns:\n", "            dict: 比较结果\n", "        \"\"\"\n", "        analysis1 = self.analyze_vocabulary_usage(texts1, labels[0])\n", "        analysis2 = self.analyze_vocabulary_usage(texts2, labels[1])\n", "        \n", "        # 创建比较图表\n", "        fig = make_subplots(\n", "            rows=2, cols=2,\n", "            subplot_titles=('词汇多样性比较', '医学词汇使用', '文本长度分布', '高频词对比'),\n", "            specs=[[{\"type\": \"bar\"}, {\"type\": \"bar\"}],\n", "                   [{\"type\": \"histogram\"}, {\"type\": \"bar\"}]]\n", "        )\n", "        \n", "        # 词汇多样性比较\n", "        fig.add_trace(\n", "            go.Bar(x=labels, y=[analysis1['vocabulary_diversity'], analysis2['vocabulary_diversity']],\n", "                  name='词汇多样性', marker_color=['blue', 'red']),\n", "            row=1, col=1\n", "        )\n", "        \n", "        # 医学词汇使用\n", "        categories = list(self.medical_categories.keys())\n", "        values1 = [analysis1['medical_usage'][cat] for cat in categories]\n", "        values2 = [analysis2['medical_usage'][cat] for cat in categories]\n", "        \n", "        fig.add_trace(\n", "            go.Bar(x=categories, y=values1, name=labels[0], marker_color='blue'),\n", "            row=1, col=2\n", "        )\n", "        fig.add_trace(\n", "            go.Bar(x=categories, y=values2, name=labels[1], marker_color='red'),\n", "            row=1, col=2\n", "        )\n", "        \n", "        # 文本长度分布\n", "        lengths1 = [len(text.split()) for text in texts1]\n", "        lengths2 = [len(text.split()) for text in texts2]\n", "        \n", "        fig.add_trace(\n", "            go.Histogram(x=lengths1, name=labels[0], opacity=0.7, marker_color='blue'),\n", "            row=2, col=1\n", "        )\n", "        fig.add_trace(\n", "            go.Histogram(x=lengths2, name=labels[1], opacity=0.7, marker_color='red'),\n", "            row=2, col=1\n", "        )\n", "        \n", "        # 高频词对比（取前10个）\n", "        common_words = set(list(analysis1['word_frequency'].keys())[:10]) | \\\n", "                      set(list(analysis2['word_frequency'].keys())[:10])\n", "        \n", "        word_comparison = []\n", "        for word in list(common_words)[:10]:\n", "            freq1 = analysis1['word_frequency'].get(word, 0)\n", "            freq2 = analysis2['word_frequency'].get(word, 0)\n", "            word_comparison.append((word, freq1, freq2))\n", "        \n", "        word_comparison.sort(key=lambda x: x[1] + x[2], reverse=True)\n", "        words, freqs1, freqs2 = zip(*word_comparison[:10])\n", "        \n", "        fig.add_trace(\n", "            go.Bar(x=list(words), y=list(freqs1), name=labels[0], marker_color='blue'),\n", "            row=2, col=2\n", "        )\n", "        fig.add_trace(\n", "            go.Bar(x=list(words), y=list(freqs2), name=labels[1], marker_color='red'),\n", "            row=2, col=2\n", "        )\n", "        \n", "        fig.update_layout(\n", "            title='词汇使用对比分析',\n", "            height=800,\n", "            showlegend=True\n", "        )\n", "        \n", "        fig.show()\n", "        \n", "        return {\n", "            'analysis1': analysis1,\n", "            'analysis2': analysis2,\n", "            'comparison_chart': fig\n", "        }\n", "\n", "# 演示词汇分析\n", "print(\"\\n=== 词汇分析演示 ===\")\n", "\n", "# 示例生成文本和参考文本\n", "generated_texts = [\n", "    \"The chest X-ray shows clear lungs with no evidence of pneumonia.\",\n", "    \"Normal heart size and clear lung fields.\",\n", "    \"No acute cardiopulmonary abnormalities identified.\",\n", "    \"Bilateral lung fields are clear without infiltrates.\",\n", "    \"The cardiac silhouette is within normal limits.\"\n", "]\n", "\n", "reference_texts = [\n", "    \"Chest radiograph demonstrates clear lung fields without pneumonia.\",\n", "    \"Heart size normal, lungs clear.\",\n", "    \"No acute abnormalities seen.\",\n", "    \"Both lungs clear, no infiltrates.\",\n", "    \"Normal cardiac size and contour.\"\n", "]\n", "\n", "vocab_analyzer = VocabularyAnalyzer()\n", "\n", "# 分析生成文本\n", "generated_analysis = vocab_analyzer.analyze_vocabulary_usage(generated_texts, \"生成文本\")\n", "print(f\"生成文本词汇多样性: {generated_analysis['vocabulary_diversity']:.3f}\")\n", "\n", "# 创建词云\n", "vocab_analyzer.create_wordcloud(generated_texts, \"生成文本词云\")\n", "\n", "# 比较词汇使用\n", "comparison_result = vocab_analyzer.compare_vocabularies(\n", "    generated_texts, reference_texts, ['生成文本', '参考文本']\n", ")\n", "\n", "print(\"词汇分析完成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 小结\n", "\n", "在本节中，我们学习了医学报告生成模型的结果可视化与分析方法：\n", "\n", "### 核心可视化技术\n", "\n", "1. **训练过程可视化**：\n", "   - 损失和准确率曲线\n", "   - 学习率变化监控\n", "   - 梯度范数分析\n", "   - 交互式图表展示\n", "\n", "2. **注意力机制可视化**：\n", "   - 单头注意力热图\n", "   - 多头注意力对比\n", "   - 跨层注意力流动\n", "   - 编码器-解码器注意力\n", "\n", "3. **错误分析可视化**：\n", "   - 错误类型分布\n", "   - 错误模式识别\n", "   - 案例研究展示\n", "   - 改进方向指导\n", "\n", "4. **词汇分析可视化**：\n", "   - 词云生成\n", "   - 词汇多样性分析\n", "   - 医学术语使用统计\n", "   - 生成文本与参考文本对比\n", "\n", "### 分析方法特点\n", "\n", "1. **多维度分析**：\n", "   - 从训练过程到最终结果\n", "   - 从整体统计到具体案例\n", "   - 从模型行为到文本质量\n", "\n", "2. **交互式展示**：\n", "   - 使用Plotly创建交互图表\n", "   - 支持缩放、筛选、悬停\n", "   - 便于深入探索数据\n", "\n", "3. **医学领域特化**：\n", "   - 关注医学术语使用\n", "   - 分析临床相关错误\n", "   - 评估专业性和准确性\n", "\n", "### 实际应用价值\n", "\n", "1. **模型调试**：\n", "   - 快速识别训练问题\n", "   - 发现模型行为异常\n", "   - 指导超参数调整\n", "\n", "2. **性能优化**：\n", "   - 理解模型注意力模式\n", "   - 识别改进机会\n", "   - 验证优化效果\n", "\n", "3. **结果展示**：\n", "   - 向利益相关者展示成果\n", "   - 支持学术论文发表\n", "   - 促进团队协作\n", "\n", "4. **质量保证**：\n", "   - 系统性错误检测\n", "   - 安全性评估\n", "   - 临床适用性验证\n", "\n", "### 最佳实践建议\n", "\n", "1. **定期监控**：\n", "   - 建立自动化监控流程\n", "   - 设置关键指标阈值\n", "   - 及时发现异常情况\n", "\n", "2. **多角度分析**：\n", "   - 结合定量和定性分析\n", "   - 考虑不同利益相关者需求\n", "   - 平衡技术细节和整体趋势\n", "\n", "3. **持续改进**：\n", "   - 根据分析结果调整策略\n", "   - 收集用户反馈\n", "   - 更新分析方法和工具\n", "\n", "### 下一步\n", "\n", "在最后一个notebook中，我们将整合所有学习内容，实现一个完整的R2Gen复现项目。\n", "\n", "### 关键收获\n", "\n", "- 可视化是理解和改进模型的重要工具\n", "- 多维度分析能够提供全面的模型洞察\n", "- 交互式图表增强了分析的深度和广度\n", "- 领域特化的分析方法更有针对性\n", "- 系统性的可视化流程支持持续改进"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}