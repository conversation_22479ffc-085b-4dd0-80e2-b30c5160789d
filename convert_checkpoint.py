#!/usr/bin/env python3
"""
转换检查点格式脚本

将我们的二阶段训练模型检查点转换为原始R2Gen能识别的格式
"""

import torch
import sys
import os

def convert_checkpoint(input_path, output_path):
    """转换检查点格式"""
    print(f"加载检查点: {input_path}")
    
    # 加载我们的检查点
    checkpoint = torch.load(input_path, map_location='cpu')
    
    print("原始检查点包含的键:")
    for key in checkpoint.keys():
        print(f"  {key}")
    
    # 创建原始R2Gen格式的检查点
    converted_checkpoint = {
        'state_dict': checkpoint['model_state_dict'],  # 关键：重命名键
        'epoch': 5,  # 我们训练了5个epoch
        'best_val_score': checkpoint.get('best_val_score', 0.0)
    }
    
    # 保存转换后的检查点
    torch.save(converted_checkpoint, output_path)
    print(f"转换后的检查点已保存到: {output_path}")
    
    # 验证转换结果
    print("\n验证转换结果:")
    converted = torch.load(output_path, map_location='cpu')
    print("转换后检查点包含的键:")
    for key in converted.keys():
        print(f"  {key}")
    
    print(f"模型参数数量: {len(converted['state_dict'])}")

if __name__ == '__main__':
    input_path = 'enhanced_image_two_stage_model/checkpoints/best_model.pth'
    output_path = 'enhanced_image_two_stage_model/checkpoints/best_model_r2gen_format.pth'
    
    if not os.path.exists(input_path):
        print(f"错误: 输入文件不存在 {input_path}")
        sys.exit(1)
    
    convert_checkpoint(input_path, output_path)
    print("\n✅ 检查点格式转换完成!")
