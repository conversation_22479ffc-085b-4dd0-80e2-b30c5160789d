{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 02 - 数据集理解与探索\n", "\n", "在这个notebook中，我们将深入探索IU X-Ray数据集，了解医学影像和报告数据的特点。这是理解整个项目的重要基础。\n", "\n", "## 学习目标\n", "\n", "- 深入理解IU X-Ray数据集的结构和组织方式\n", "- 探索医学影像的特点和格式\n", "- 分析放射学报告的文本特征\n", "- 了解数据集的统计信息和分布\n", "- 可视化数据样本，建立直观认识\n", "\n", "## 医学影像报告生成任务简介\n", "\n", "医学影像报告生成是一个多模态任务，需要：\n", "\n", "1. **输入**：医学影像（如X光片、CT、MRI等）\n", "2. **输出**：描述影像内容的自然语言报告\n", "3. **挑战**：\n", "   - 医学术语的专业性\n", "   - 影像细节的准确描述\n", "   - 病理发现的识别\n", "   - 报告结构的规范性"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 导入必要的库\n", "import os\n", "import json\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import pandas as pd\n", "from collections import Counter\n", "import re\n", "from PIL import Image\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# 设置中文字体（如果需要显示中文）\n", "plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "# 设置项目根目录\n", "project_root = os.path.abspath('..')\n", "print(f\"项目根目录: {project_root}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 数据集结构分析\n", "\n", "首先，让我们详细分析IU X-Ray数据集的结构："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 加载数据集标注文件\n", "annotation_path = os.path.join(project_root, 'data', 'iu_xray', 'annotation.json')\n", "image_dir = os.path.join(project_root, 'data', 'iu_xray', 'images')\n", "\n", "try:\n", "    with open(annotation_path, 'r') as f:\n", "        data = json.load(f)\n", "    \n", "    print(\"数据集加载成功！\")\n", "    print(f\"数据集包含的分割: {list(data.keys())}\")\n", "    \n", "    # 统计各分割的样本数量\n", "    for split in data.keys():\n", "        print(f\"{split}集样本数: {len(data[split])}\")\n", "        \n", "except FileNotFoundError:\n", "    print(f\"找不到标注文件: {annotation_path}\")\n", "    print(\"请确保已下载IU X-Ray数据集\")\n", "    data = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 数据样本结构分析\n", "\n", "让我们详细查看数据样本的结构："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if data is not None:\n", "    # 查看第一个训练样本的结构\n", "    sample = data['train'][0]\n", "    print(\"样本结构:\")\n", "    for key, value in sample.items():\n", "        if key == 'report':\n", "            print(f\"{key}: {value[:100]}...\" if len(value) > 100 else f\"{key}: {value}\")\n", "        else:\n", "            print(f\"{key}: {value}\")\n", "    \n", "    print(\"\\n样本字段说明:\")\n", "    print(\"- id: 样本的唯一标识符\")\n", "    print(\"- report: 放射学报告文本\")\n", "    print(\"- image_path: 对应的X光图像路径列表（通常包含2张：正面和侧面）\")\n", "    print(\"- split: 数据分割标识（train/val/test）\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 图像数据探索\n", "\n", "IU X-Ray数据集中的每个病例通常包含两张不同角度的X光片：正面（frontal）和侧面（lateral）。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if data is not None:\n", "    # 分析图像路径信息\n", "    all_samples = data['train'] + data['val'] + data['test']\n", "    \n", "    # 统计每个样本的图像数量\n", "    image_counts = [len(sample['image_path']) for sample in all_samples]\n", "    image_count_stats = Counter(image_counts)\n", "    \n", "    print(\"每个样本的图像数量分布:\")\n", "    for count, freq in sorted(image_count_stats.items()):\n", "        print(f\"{count}张图像: {freq}个样本\")\n", "    \n", "    # 检查图像文件是否存在\n", "    sample_with_images = data['train'][0]\n", "    print(f\"\\n检查样本 {sample_with_images['id']} 的图像:\")\n", "    \n", "    for i, img_path in enumerate(sample_with_images['image_path']):\n", "        full_path = os.path.join(image_dir, img_path)\n", "        exists = os.path.exists(full_path)\n", "        print(f\"图像 {i+1}: {img_path} - {'存在' if exists else '不存在'}\")\n", "        \n", "        if exists:\n", "            try:\n", "                img = Image.open(full_path)\n", "                print(f\"  尺寸: {img.size}, 模式: {img.mode}\")\n", "            except Exception as e:\n", "                print(f\"  读取图像时出错: {e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 可视化医学影像样本\n", "\n", "让我们可视化一些X光图像样本，了解数据的视觉特征："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def visualize_sample(sample_data, image_dir, title=\"医学影像样本\"):\n", "    \"\"\"可视化一个数据样本的图像和报告\"\"\"\n", "    fig, axes = plt.subplots(1, 2, figsize=(15, 6))\n", "    \n", "    # 显示图像\n", "    for i, img_path in enumerate(sample_data['image_path']):\n", "        full_path = os.path.join(image_dir, img_path)\n", "        if os.path.exists(full_path):\n", "            try:\n", "                img = Image.open(full_path)\n", "                axes[i].imshow(img, cmap='gray')\n", "                axes[i].set_title(f\"图像 {i+1}: {os.path.basename(img_path)}\")\n", "                axes[i].axis('off')\n", "            except Exception as e:\n", "                axes[i].text(0.5, 0.5, f\"无法加载图像\\n{e}\", \n", "                           ha='center', va='center', transform=axes[i].transAxes)\n", "                axes[i].set_title(f\"图像 {i+1}: 加载失败\")\n", "        else:\n", "            axes[i].text(0.5, 0.5, \"图像文件不存在\", \n", "                       ha='center', va='center', transform=axes[i].transAxes)\n", "            axes[i].set_title(f\"图像 {i+1}: 文件不存在\")\n", "    \n", "    plt.suptitle(f\"{title} - ID: {sample_data['id']}\")\n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # 显示报告\n", "    print(f\"\\n放射学报告:\")\n", "    print(f\"ID: {sample_data['id']}\")\n", "    print(f\"报告内容: {sample_data['report']}\")\n", "    print(\"-\" * 80)\n", "\n", "if data is not None:\n", "    # 可视化前3个训练样本\n", "    for i in range(min(3, len(data['train']))):\n", "        visualize_sample(data['train'][i], image_dir, f\"训练样本 {i+1}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 报告文本分析\n", "\n", "现在让我们深入分析放射学报告的文本特征："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if data is not None:\n", "    # 收集所有报告文本\n", "    all_reports = []\n", "    for split in ['train', 'val', 'test']:\n", "        for sample in data[split]:\n", "            all_reports.append(sample['report'])\n", "    \n", "    print(f\"总报告数量: {len(all_reports)}\")\n", "    \n", "    # 分析报告长度\n", "    report_lengths = [len(report.split()) for report in all_reports]\n", "    \n", "    print(f\"\\n报告长度统计（单词数）:\")\n", "    print(f\"平均长度: {np.mean(report_lengths):.2f}\")\n", "    print(f\"中位数长度: {np.median(report_lengths):.2f}\")\n", "    print(f\"最短报告: {min(report_lengths)} 单词\")\n", "    print(f\"最长报告: {max(report_lengths)} 单词\")\n", "    print(f\"标准差: {np.std(report_lengths):.2f}\")\n", "    \n", "    # 可视化报告长度分布\n", "    plt.figure(figsize=(12, 4))\n", "    \n", "    plt.subplot(1, 2, 1)\n", "    plt.hist(report_lengths, bins=50, alpha=0.7, edgecolor='black')\n", "    plt.xlabel('报告长度（单词数）')\n", "    plt.ylabel('频次')\n", "    plt.title('报告长度分布')\n", "    plt.grid(True, alpha=0.3)\n", "    \n", "    plt.subplot(1, 2, 2)\n", "    plt.boxplot(report_lengths)\n", "    plt.ylabel('报告长度（单词数）')\n", "    plt.title('报告长度箱线图')\n", "    plt.grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 词汇分析\n", "\n", "让我们分析报告中的词汇特征："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if data is not None:\n", "    # 文本预处理函数（简化版）\n", "    def simple_clean_text(text):\n", "        \"\"\"简单的文本清理\"\"\"\n", "        # 转换为小写\n", "        text = text.lower()\n", "        # 移除标点符号\n", "        text = re.sub(r'[^a-zA-Z\\s]', '', text)\n", "        # 分割单词\n", "        words = text.split()\n", "        return words\n", "    \n", "    # 收集所有单词\n", "    all_words = []\n", "    for report in all_reports:\n", "        words = simple_clean_text(report)\n", "        all_words.extend(words)\n", "    \n", "    # 统计词频\n", "    word_counts = Counter(all_words)\n", "    \n", "    print(f\"总词汇数量: {len(all_words)}\")\n", "    print(f\"唯一词汇数量: {len(word_counts)}\")\n", "    print(f\"平均每个词汇出现次数: {len(all_words) / len(word_counts):.2f}\")\n", "    \n", "    # 显示最常见的词汇\n", "    print(\"\\n最常见的30个词汇:\")\n", "    most_common = word_counts.most_common(30)\n", "    for i, (word, count) in enumerate(most_common, 1):\n", "        print(f\"{i:2d}. {word:15s} ({count:4d}次)\")\n", "    \n", "    # 可视化词频分布\n", "    plt.figure(figsize=(15, 5))\n", "    \n", "    # 最常见词汇的条形图\n", "    plt.subplot(1, 2, 1)\n", "    words, counts = zip(*most_common[:20])\n", "    plt.barh(range(len(words)), counts)\n", "    plt.yticks(range(len(words)), words)\n", "    plt.xlabel('出现次数')\n", "    plt.title('最常见的20个词汇')\n", "    plt.gca().invert_yaxis()\n", "    \n", "    # 词频分布（对数尺度）\n", "    plt.subplot(1, 2, 2)\n", "    freq_counts = list(word_counts.values())\n", "    plt.hist(freq_counts, bins=50, alpha=0.7, edgecolor='black')\n", "    plt.xlabel('词汇出现次数')\n", "    plt.ylabel('词汇数量')\n", "    plt.title('词频分布')\n", "    plt.yscale('log')\n", "    plt.grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 医学术语分析\n", "\n", "让我们识别和分析报告中的医学术语："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if data is not None:\n", "    # 定义一些常见的医学术语\n", "    medical_terms = {\n", "        '解剖结构': ['heart', 'lung', 'lungs', 'cardiac', 'pulmonary', 'mediastinal', \n", "                   'diaphragm', 'pleural', 'thoracic', 'spine', 'ribs', 'clavicle'],\n", "        '病理发现': ['pneumonia', 'effusion', 'pneumothorax', 'consolidation', \n", "                   'atelectasis', 'edema', 'opacity', 'infiltrate', 'nodule', 'mass'],\n", "        '描述词汇': ['normal', 'abnormal', 'enlarged', 'clear', 'stable', 'unchanged', \n", "                   'increased', 'decreased', 'mild', 'moderate', 'severe'],\n", "        '位置描述': ['bilateral', 'left', 'right', 'upper', 'lower', 'base', 'apex', \n", "                   'central', 'peripheral', 'anterior', 'posterior']\n", "    }\n", "    \n", "    # 统计各类医学术语的出现频率\n", "    term_stats = {}\n", "    for category, terms in medical_terms.items():\n", "        category_counts = {}\n", "        for term in terms:\n", "            count = word_counts.get(term, 0)\n", "            if count > 0:\n", "                category_counts[term] = count\n", "        term_stats[category] = category_counts\n", "    \n", "    # 显示各类术语的统计\n", "    for category, counts in term_stats.items():\n", "        print(f\"\\n{category}术语使用频率:\")\n", "        sorted_terms = sorted(counts.items(), key=lambda x: x[1], reverse=True)\n", "        for term, count in sorted_terms[:10]:  # 显示前10个\n", "            print(f\"  {term:15s}: {count:4d}次\")\n", "    \n", "    # 可视化医学术语分布\n", "    plt.figure(figsize=(15, 10))\n", "    \n", "    for i, (category, counts) in enumerate(term_stats.items(), 1):\n", "        plt.subplot(2, 2, i)\n", "        if counts:\n", "            terms, freqs = zip(*sorted(counts.items(), key=lambda x: x[1], reverse=True)[:10])\n", "            plt.barh(range(len(terms)), freqs)\n", "            plt.yticks(range(len(terms)), terms)\n", "            plt.xlabel('出现次数')\n", "            plt.title(f'{category}术语频率')\n", "            plt.gca().invert_yaxis()\n", "        else:\n", "            plt.text(0.5, 0.5, '无相关术语', ha='center', va='center', transform=plt.gca().transAxes)\n", "            plt.title(f'{category}术语频率')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 数据质量分析\n", "\n", "让我们分析数据集的质量，包括缺失值、异常值等："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if data is not None:\n", "    # 检查数据完整性\n", "    print(\"数据完整性检查:\")\n", "    \n", "    total_samples = 0\n", "    missing_reports = 0\n", "    missing_images = 0\n", "    empty_reports = 0\n", "    \n", "    for split in ['train', 'val', 'test']:\n", "        for sample in data[split]:\n", "            total_samples += 1\n", "            \n", "            # 检查报告\n", "            if 'report' not in sample or sample['report'] is None:\n", "                missing_reports += 1\n", "            elif len(sample['report'].strip()) == 0:\n", "                empty_reports += 1\n", "            \n", "            # 检查图像路径\n", "            if 'image_path' not in sample or not sample['image_path']:\n", "                missing_images += 1\n", "    \n", "    print(f\"总样本数: {total_samples}\")\n", "    print(f\"缺失报告: {missing_reports} ({missing_reports/total_samples*100:.2f}%)\")\n", "    print(f\"空报告: {empty_reports} ({empty_reports/total_samples*100:.2f}%)\")\n", "    print(f\"缺失图像路径: {missing_images} ({missing_images/total_samples*100:.2f}%)\")\n", "    \n", "    # 检查报告长度异常值\n", "    print(\"\\n报告长度异常值检查:\")\n", "    q1 = np.percentile(report_lengths, 25)\n", "    q3 = np.percentile(report_lengths, 75)\n", "    iqr = q3 - q1\n", "    lower_bound = q1 - 1.5 * iqr\n", "    upper_bound = q3 + 1.5 * iqr\n", "    \n", "    outliers = [length for length in report_lengths if length < lower_bound or length > upper_bound]\n", "    print(f\"异常值数量: {len(outliers)} ({len(outliers)/len(report_lengths)*100:.2f}%)\")\n", "    print(f\"正常范围: {lower_bound:.1f} - {upper_bound:.1f} 单词\")\n", "    \n", "    # 显示一些极端样本\n", "    print(\"\\n极短报告示例:\")\n", "    short_reports = [(i, report) for i, report in enumerate(all_reports) \n", "                    if len(report.split()) <= 5]\n", "    for i, (idx, report) in enumerate(short_reports[:3]):\n", "        print(f\"{i+1}. ({len(report.split())}词): {report}\")\n", "    \n", "    print(\"\\n极长报告示例:\")\n", "    long_reports = [(i, report) for i, report in enumerate(all_reports) \n", "                   if len(report.split()) >= 100]\n", "    for i, (idx, report) in enumerate(long_reports[:2]):\n", "        print(f\"{i+1}. ({len(report.split())}词): {report[:200]}...\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 数据集分割分析\n", "\n", "分析训练集、验证集和测试集的分布："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if data is not None:\n", "    # 分析各分割的统计信息\n", "    split_stats = {}\n", "    \n", "    for split in ['train', 'val', 'test']:\n", "        split_data = data[split]\n", "        split_reports = [sample['report'] for sample in split_data]\n", "        split_lengths = [len(report.split()) for report in split_reports]\n", "        \n", "        # 收集该分割的所有单词\n", "        split_words = []\n", "        for report in split_reports:\n", "            words = simple_clean_text(report)\n", "            split_words.extend(words)\n", "        \n", "        split_stats[split] = {\n", "            'samples': len(split_data),\n", "            'avg_length': np.mean(split_lengths),\n", "            'total_words': len(split_words),\n", "            'unique_words': len(set(split_words)),\n", "            'vocab_diversity': len(set(split_words)) / len(split_words) if split_words else 0\n", "        }\n", "    \n", "    # 创建统计表格\n", "    stats_df = pd.DataFrame(split_stats).T\n", "    print(\"数据集分割统计:\")\n", "    print(stats_df.round(3))\n", "    \n", "    # 可视化分割统计\n", "    fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "    \n", "    # 样本数量\n", "    axes[0, 0].bar(stats_df.index, stats_df['samples'])\n", "    axes[0, 0].set_title('各分割样本数量')\n", "    axes[0, 0].set_ylabel('样本数')\n", "    \n", "    # 平均报告长度\n", "    axes[0, 1].bar(stats_df.index, stats_df['avg_length'])\n", "    axes[0, 1].set_title('平均报告长度')\n", "    axes[0, 1].set_ylabel('单词数')\n", "    \n", "    # 总词汇数\n", "    axes[1, 0].bar(stats_df.index, stats_df['total_words'])\n", "    axes[1, 0].set_title('总词汇数')\n", "    axes[1, 0].set_ylabel('词汇数')\n", "    \n", "    # 词汇多样性\n", "    axes[1, 1].bar(stats_df.index, stats_df['vocab_diversity'])\n", "    axes[1, 1].set_title('词汇多样性')\n", "    axes[1, 1].set_ylabel('唯一词汇比例')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 小结\n", "\n", "通过本节的探索，我们了解到：\n", "\n", "### 数据集特点\n", "- IU X-Ray数据集包含训练、验证和测试三个分割\n", "- 每个样本通常包含2张不同角度的X光图像\n", "- 报告文本长度变化较大，平均约20-30个单词\n", "- 包含丰富的医学术语和专业描述\n", "\n", "### 文本特征\n", "- 报告使用标准的医学术语\n", "- 常见词汇包括解剖结构、病理发现、描述性词汇等\n", "- 文本结构相对规范，适合自动化处理\n", "\n", "### 数据质量\n", "- 数据完整性良好，缺失值很少\n", "- 存在一些长度异常的报告，需要在预处理时注意\n", "- 各分割的统计特征相对均衡\n", "\n", "### 下一步\n", "在下一个notebook中，我们将学习如何对这些数据进行预处理，包括：\n", "- 图像的标准化和增强\n", "- 文本的清理和标记化\n", "- 数据加载器的实现\n", "- 批处理的组织方式\n", "\n", "这些预处理步骤是训练深度学习模型的重要基础。"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}