#!/usr/bin/env python3
"""
二阶段训练器

功能：
1. 使用预计算的视觉特征进行训练
2. 跳过视觉编码器，直接训练编码器-解码器
3. 提供与原始训练器兼容的接口
"""

import os
import sys
import torch
import torch.nn as nn
from torch.optim import Adam
from torch.optim.lr_scheduler import StepLR
import numpy as np
from tqdm import tqdm
import json
from pathlib import Path

# 添加R2Gen路径
sys.path.append('../R2Gen-main')

from models.r2gen import R2GenModel
from modules.loss import compute_loss
from modules.metrics import compute_scores
from modules.optimizers import build_optimizer, build_lr_scheduler
from modules.tokenizers import Tokenizer

from stage2_dataloader import create_stage2_dataloader


class Stage2Trainer:
    """
    二阶段训练器
    
    使用预计算的视觉特征训练编码器-解码器部分
    """
    
    def __init__(self, args):
        """
        初始化训练器
        
        Args:
            args: 训练参数
        """
        self.args = args
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        print(f"Stage2Trainer 初始化:")
        print(f"  设备: {self.device}")
        print(f"  特征目录: {args.features_dir}")
        print(f"  保存目录: {args.save_dir}")
        
        # 创建保存目录
        os.makedirs(args.save_dir, exist_ok=True)
        
        # 初始化分词器
        self.tokenizer = Tokenizer(args)
        
        # 初始化模型
        self._build_model()
        
        # 初始化数据加载器
        self._build_dataloaders()
        
        # 初始化优化器和调度器
        self._build_optimizer()
        
        # 训练状态
        self.current_epoch = 0
        self.best_val_score = 0.0
        self.train_losses = []
        self.val_scores = []
    
    def _build_model(self):
        """构建模型"""
        print("构建二阶段模型...")
        
        # 创建完整的R2Gen模型
        self.model = R2GenModel(self.args, self.tokenizer)
        
        # 冻结视觉编码器（因为我们使用预计算特征）
        for param in self.model.visual_extractor.parameters():
            param.requires_grad = False
        
        print(f"  视觉编码器已冻结")
        
        # 移动到设备
        self.model = self.model.to(self.device)
        
        # 计算可训练参数
        total_params = sum(p.numel() for p in self.model.parameters())
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        
        print(f"  总参数数: {total_params:,}")
        print(f"  可训练参数: {trainable_params:,}")
        print(f"  冻结参数: {total_params - trainable_params:,}")
    
    def _build_dataloaders(self):
        """构建数据加载器"""
        print("构建数据加载器...")
        
        # 训练集
        self.train_dataloader = create_stage2_dataloader(
            features_dir=self.args.features_dir,
            ann_path=self.args.ann_path,
            split='train',
            tokenizer=self.tokenizer,
            batch_size=self.args.batch_size,
            shuffle=True,
            num_workers=self.args.num_workers,
            max_seq_length=self.args.max_seq_length
        )
        
        # 验证集
        self.val_dataloader = create_stage2_dataloader(
            features_dir=self.args.features_dir,
            ann_path=self.args.ann_path,
            split='val',
            tokenizer=self.tokenizer,
            batch_size=self.args.batch_size,
            shuffle=False,
            num_workers=self.args.num_workers,
            max_seq_length=self.args.max_seq_length
        )
        
        # 测试集
        self.test_dataloader = create_stage2_dataloader(
            features_dir=self.args.features_dir,
            ann_path=self.args.ann_path,
            split='test',
            tokenizer=self.tokenizer,
            batch_size=self.args.batch_size,
            shuffle=False,
            num_workers=self.args.num_workers,
            max_seq_length=self.args.max_seq_length
        )
        
        print(f"数据加载器构建完成:")
        print(f"  训练批次: {len(self.train_dataloader)}")
        print(f"  验证批次: {len(self.val_dataloader)}")
        print(f"  测试批次: {len(self.test_dataloader)}")
    
    def _build_optimizer(self):
        """构建优化器和调度器"""
        print("构建优化器...")
        
        # 只优化可训练参数
        trainable_params = [p for p in self.model.parameters() if p.requires_grad]
        
        self.optimizer = Adam(
            trainable_params,
            lr=self.args.lr_ed,  # 使用编码器-解码器学习率
            weight_decay=self.args.weight_decay
        )
        
        self.lr_scheduler = StepLR(
            self.optimizer,
            step_size=self.args.step_size,
            gamma=self.args.gamma
        )
        
        print(f"  优化器: Adam")
        print(f"  学习率: {self.args.lr_ed}")
        print(f"  权重衰减: {self.args.weight_decay}")
    
    def train_epoch(self):
        """训练一个epoch"""
        self.model.train()
        
        total_loss = 0.0
        num_batches = len(self.train_dataloader)
        
        progress_bar = tqdm(
            self.train_dataloader,
            desc=f"Epoch {self.current_epoch + 1} Training",
            leave=False
        )
        
        for batch_idx, batch in enumerate(progress_bar):
            # 移动数据到设备
            att_feats = batch['att_feats'].to(self.device)  # (B, 98, 2048)
            fc_feats = batch['fc_feats'].to(self.device)    # (B, 4096)
            labels = batch['labels'].to(self.device)        # (B, seq_len)
            masks = batch['masks'].to(self.device)          # (B, seq_len)
            
            # 清零梯度
            self.optimizer.zero_grad()
            
            # 前向传播（跳过视觉编码器）
            # 直接使用预计算的特征进行编码器-解码器训练
            output = self.model.encoder_decoder(fc_feats, att_feats, labels, mode='forward')
            
            # 计算损失
            loss = compute_loss(output, labels, masks)
            
            # 反向传播
            loss.backward()
            
            # 梯度裁剪
            if hasattr(self.args, 'grad_clip') and self.args.grad_clip > 0:
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.args.grad_clip)
            
            # 更新参数
            self.optimizer.step()
            
            # 累计损失
            total_loss += loss.item()
            
            # 更新进度条
            avg_loss = total_loss / (batch_idx + 1)
            progress_bar.set_postfix({'loss': f'{avg_loss:.4f}'})
        
        # 更新学习率
        self.lr_scheduler.step()
        
        avg_loss = total_loss / num_batches
        self.train_losses.append(avg_loss)
        
        return avg_loss
    
    def validate(self, dataloader, split_name='val'):
        """
        验证模型 - 使用真正的文本生成和评估指标

        Args:
            dataloader: 数据加载器
            split_name: 数据集分割名称

        Returns:
            dict: 评估指标字典
        """
        self.model.eval()

        total_loss = 0.0
        num_batches = len(dataloader)

        # 收集生成的报告和真实报告
        generated_reports = []
        ground_truth_reports = []

        with torch.no_grad():
            progress_bar = tqdm(
                dataloader,
                desc=f"{split_name.capitalize()} Evaluation",
                leave=False
            )

            for batch in progress_bar:
                # 移动数据到设备
                att_feats = batch['att_feats'].to(self.device)
                fc_feats = batch['fc_feats'].to(self.device)
                labels = batch['labels'].to(self.device)
                masks = batch['masks'].to(self.device)

                # 计算损失（用于训练监控）
                output = self.model.encoder_decoder(fc_feats, att_feats, labels, mode='forward')
                loss = compute_loss(output, labels, masks)
                total_loss += loss.item()

                # 生成报告文本（用于评估指标）
                # 使用与原始R2Gen相同的采样方法
                generated_output = self.model.encoder_decoder(fc_feats, att_feats, mode='sample')

                # 提取生成的序列（第一个元素是序列，第二个是log概率）
                if isinstance(generated_output, tuple):
                    generated_ids = generated_output[0]
                else:
                    generated_ids = generated_output

                # 解码生成的报告
                generated_texts = self.model.tokenizer.decode_batch(generated_ids.cpu().numpy())

                # 解码真实报告（跳过BOS token）
                ground_truth_texts = self.model.tokenizer.decode_batch(labels[:, 1:].cpu().numpy())

                # 收集结果
                generated_reports.extend(generated_texts)
                ground_truth_reports.extend(ground_truth_texts)

        avg_loss = total_loss / num_batches

        # 计算真实的评估指标
        if len(generated_reports) > 0 and len(ground_truth_reports) > 0:
            # 构建评估格式：{id: [text]}
            gts = {i: [gt] for i, gt in enumerate(ground_truth_reports)}
            res = {i: [gen] for i, gen in enumerate(generated_reports)}

            # 使用R2Gen的评估函数
            scores = compute_scores(gts, res)
            scores['loss'] = avg_loss
        else:
            # 如果没有数据，返回零分
            scores = {
                'BLEU_1': 0.0,
                'BLEU_2': 0.0,
                'BLEU_3': 0.0,
                'BLEU_4': 0.0,
                'METEOR': 0.0,
                'ROUGE_L': 0.0,
                'CIDEr': 0.0,
                'loss': avg_loss
            }

        return scores
    
    def train(self):
        """主训练循环"""
        print(f"\n开始二阶段训练...")
        print(f"训练轮数: {self.args.epochs}")
        print(f"批次大小: {self.args.batch_size}")
        
        for epoch in range(self.args.epochs):
            self.current_epoch = epoch
            
            print(f"\n=== Epoch {epoch + 1}/{self.args.epochs} ===")
            
            # 训练
            train_loss = self.train_epoch()
            print(f"训练损失: {train_loss:.4f}")
            
            # 验证
            val_scores = self.validate(self.val_dataloader, 'val')
            val_bleu4 = val_scores['BLEU_4']
            print(f"验证 BLEU-4: {val_bleu4:.4f}")
            
            # 保存最佳模型
            if val_bleu4 > self.best_val_score:
                self.best_val_score = val_bleu4
                self.save_checkpoint('best_model.pth')
                print(f"✅ 保存最佳模型 (BLEU-4: {val_bleu4:.4f})")
            
            # 保存当前模型
            self.save_checkpoint(f'epoch_{epoch + 1}.pth')
            
            self.val_scores.append(val_scores)
        
        print(f"\n训练完成！最佳验证 BLEU-4: {self.best_val_score:.4f}")
        
        # 测试最佳模型
        self.load_checkpoint('best_model.pth')
        test_scores = self.validate(self.test_dataloader, 'test')
        print(f"测试结果:")
        for metric, score in test_scores.items():
            print(f"  {metric}: {score:.4f}")
        
        return test_scores
    
    def save_checkpoint(self, filename):
        """保存检查点"""
        checkpoint = {
            'epoch': self.current_epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'lr_scheduler_state_dict': self.lr_scheduler.state_dict(),
            'best_val_score': self.best_val_score,
            'train_losses': self.train_losses,
            'val_scores': self.val_scores
        }

        filepath = os.path.join(self.args.save_dir, filename)
        torch.save(checkpoint, filepath)
    
    def load_checkpoint(self, filename):
        """加载检查点"""
        filepath = os.path.join(self.args.save_dir, filename)
        checkpoint = torch.load(filepath, map_location=self.device)
        
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.lr_scheduler.load_state_dict(checkpoint['lr_scheduler_state_dict'])
        self.current_epoch = checkpoint['epoch']
        self.best_val_score = checkpoint['best_val_score']
        self.train_losses = checkpoint['train_losses']
        self.val_scores = checkpoint['val_scores']
