{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 04 - CNN特征提取器详解\n", "\n", "在这个notebook中，我们将深入理解卷积神经网络(CNN)的工作原理，特别是R2Gen项目中使用的ResNet101视觉特征提取器。\n", "\n", "## 学习目标\n", "\n", "- 理解卷积神经网络的基本原理\n", "- 掌握ResNet架构和残差连接的概念\n", "- 学习如何从医学影像中提取视觉特征\n", "- 可视化特征提取过程和特征图\n", "- 理解预训练模型的使用和微调\n", "\n", "## CNN基础概念\n", "\n", "卷积神经网络是专门用于处理图像数据的深度学习模型。它的核心思想是：\n", "\n", "1. **局部连接**：每个神经元只与输入的局部区域连接\n", "2. **权重共享**：同一个卷积核在整个图像上共享参数\n", "3. **平移不变性**：对图像的平移具有一定的鲁棒性\n", "4. **层次特征**：从低级特征（边缘、纹理）到高级特征（形状、对象）\n", "\n", "### CNN的主要组件\n", "\n", "- **卷积层(Convolution)**：提取局部特征\n", "- **池化层(Pooling)**：降低空间维度，增加感受野\n", "- **激活函数(Activation)**：引入非线性\n", "- **批归一化(Batch Normalization)**：稳定训练过程\n", "- **全连接层(Fully Connected)**：最终的分类或回归"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 导入必要的库\n", "import os\n", "import sys\n", "import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "import torchvision.models as models\n", "import torchvision.transforms as transforms\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from PIL import Image\n", "import json\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# 设置项目路径\n", "project_root = os.path.abspath('..')\n", "sys.path.append(project_root)\n", "\n", "print(f\"项目根目录: {project_root}\")\n", "print(f\"PyTorch版本: {torch.__version__}\")\n", "print(f\"CUDA可用: {torch.cuda.is_available()}\")\n", "\n", "# 设置设备\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f\"使用设备: {device}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 卷积操作详解\n", "\n", "让我们从最基础的卷积操作开始理解CNN："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def visualize_convolution(input_image, kernel, stride=1, padding=0):\n", "    \"\"\"\n", "    可视化卷积操作过程\n", "    \n", "    Args:\n", "        input_image (torch.Tensor): 输入图像\n", "        kernel (torch.Tensor): 卷积核\n", "        stride (int): 步长\n", "        padding (int): 填充\n", "    \"\"\"\n", "    # 执行卷积操作\n", "    conv_layer = nn.Conv2d(1, 1, kernel_size=kernel.shape[-1], \n", "                          stride=stride, padding=padding, bias=False)\n", "    \n", "    # 设置卷积核权重\n", "    with torch.no_grad():\n", "        conv_layer.weight = nn.Parameter(kernel.unsqueeze(0).unsqueeze(0))\n", "    \n", "    # 应用卷积\n", "    output = conv_layer(input_image.unsqueeze(0).unsqueeze(0))\n", "    \n", "    # 可视化\n", "    fig, axes = plt.subplots(1, 3, figsize=(15, 4))\n", "    \n", "    # 输入图像\n", "    axes[0].imshow(input_image.numpy(), cmap='gray')\n", "    axes[0].set_title(f'输入图像 {input_image.shape}')\n", "    axes[0].axis('off')\n", "    \n", "    # 卷积核\n", "    axes[1].imshow(kernel.numpy(), cmap='RdBu', vmin=-1, vmax=1)\n", "    axes[1].set_title(f'卷积核 {kernel.shape}')\n", "    axes[1].axis('off')\n", "    \n", "    # 输出特征图\n", "    axes[2].imshow(output.squeeze().detach().numpy(), cmap='viridis')\n", "    axes[2].set_title(f'输出特征图 {output.squeeze().shape}')\n", "    axes[2].axis('off')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    return output\n", "\n", "# 创建示例输入图像（简单的边缘图案）\n", "input_img = torch.zeros(8, 8)\n", "input_img[2:6, 2:6] = 1.0  # 中心方块\n", "input_img[3:5, 3:5] = 0.5  # 内部方块\n", "\n", "# 定义不同类型的卷积核\n", "kernels = {\n", "    '边缘检测(垂直)': torch.tensor([[-1, 0, 1],\n", "                                [-1, 0, 1],\n", "                                [-1, 0, 1]], dtype=torch.float32),\n", "    \n", "    '边缘检测(水平)': torch.tensor([[-1, -1, -1],\n", "                                [ 0,  0,  0],\n", "                                [ 1,  1,  1]], dtype=torch.float32),\n", "    \n", "    '模糊': torch.tensor([[1, 1, 1],\n", "                        [1, 1, 1],\n", "                        [1, 1, 1]], dtype=torch.float32) / 9,\n", "    \n", "    '锐化': torch.tensor([[ 0, -1,  0],\n", "                        [-1,  5, -1],\n", "                        [ 0, -1,  0]], dtype=torch.float32)\n", "}\n", "\n", "print(\"卷积操作演示:\")\n", "for name, kernel in kernels.items():\n", "    print(f\"\\n{name}卷积核:\")\n", "    output = visualize_convolution(input_img, kernel, padding=1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## ResNet架构详解\n", "\n", "ResNet(Residual Network)是R2Gen项目中使用的视觉特征提取器。它的核心创新是**残差连接(Residual Connection)**，解决了深度网络训练中的梯度消失问题。\n", "\n", "### 残差块(Residual Block)\n", "\n", "残差块的核心思想是学习残差函数 F(x) = H(x) - x，而不是直接学习 H(x)。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class BasicResidualBlock(nn.Module):\n", "    \"\"\"\n", "    基础残差块实现\n", "    \n", "    结构：\n", "    x -> Conv -> BN -> ReLU -> Conv -> BN -> (+) -> ReLU\n", "    |                                        ^\n", "    |________________________________________|\n", "    \"\"\"\n", "    \n", "    def __init__(self, in_channels, out_channels, stride=1):\n", "        super(BasicResidual<PERSON><PERSON>, self).__init__()\n", "        \n", "        # 主路径\n", "        self.conv1 = nn.Conv2d(in_channels, out_channels, kernel_size=3, \n", "                              stride=stride, padding=1, bias=False)\n", "        self.bn1 = nn.BatchNorm2d(out_channels)\n", "        self.relu = nn.ReLU(inplace=True)\n", "        \n", "        self.conv2 = nn.Conv2d(out_channels, out_channels, kernel_size=3, \n", "                              stride=1, padding=1, bias=False)\n", "        self.bn2 = nn.BatchNorm2d(out_channels)\n", "        \n", "        # 跳跃连接（如果输入输出维度不同）\n", "        self.shortcut = nn.Sequential()\n", "        if stride != 1 or in_channels != out_channels:\n", "            self.shortcut = nn.Sequential(\n", "                nn.Conv2d(in_channels, out_channels, kernel_size=1, \n", "                         stride=stride, bias=False),\n", "                nn.BatchNorm2d(out_channels)\n", "            )\n", "    \n", "    def forward(self, x):\n", "        # 主路径\n", "        out = self.conv1(x)\n", "        out = self.bn1(out)\n", "        out = self.relu(out)\n", "        \n", "        out = self.conv2(out)\n", "        out = self.bn2(out)\n", "        \n", "        # 残差连接\n", "        out += self.shortcut(x)\n", "        out = self.relu(out)\n", "        \n", "        return out\n", "\n", "# 演示残差块的工作原理\n", "def demonstrate_residual_block():\n", "    \"\"\"\n", "    演示残差块的前向传播过程\n", "    \"\"\"\n", "    # 创建残差块\n", "    block = BasicResidualBlock(64, 64)\n", "    \n", "    # 创建输入张量\n", "    x = torch.randn(1, 64, 32, 32)\n", "    \n", "    print(\"残差块演示:\")\n", "    print(f\"输入形状: {x.shape}\")\n", "    \n", "    # 前向传播\n", "    with torch.no_grad():\n", "        # 主路径\n", "        conv1_out = block.relu(block.bn1(block.conv1(x)))\n", "        conv2_out = block.bn2(block.conv2(conv1_out))\n", "        \n", "        # 跳跃连接\n", "        shortcut_out = block.shortcut(x)\n", "        \n", "        # 残差连接\n", "        residual_out = conv2_out + shortcut_out\n", "        final_out = block.relu(residual_out)\n", "        \n", "        print(f\"第一个卷积后: {conv1_out.shape}\")\n", "        print(f\"第二个卷积后: {conv2_out.shape}\")\n", "        print(f\"跳跃连接后: {shortcut_out.shape}\")\n", "        print(f\"残差连接后: {residual_out.shape}\")\n", "        print(f\"最终输出: {final_out.shape}\")\n", "        \n", "        # 验证与直接调用的结果一致\n", "        direct_out = block(x)\n", "        print(f\"\\n直接调用输出形状: {direct_out.shape}\")\n", "        print(f\"结果一致性检查: {torch.allclose(final_out, direct_out)}\")\n", "\n", "demonstrate_residual_block()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## R2Gen中的视觉特征提取器\n", "\n", "现在让我们看看R2Gen项目中实际使用的视觉特征提取器："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 导入R2Gen的视觉特征提取器\n", "try:\n", "    from modules.visual_extractor import VisualExtractor\n", "    r2gen_available = True\n", "except ImportError:\n", "    print(\"无法导入R2Gen模块，将使用简化版本演示\")\n", "    r2gen_available = False\n", "\n", "class SimplifiedVisualExtractor(nn.Module):\n", "    \"\"\"\n", "    简化版视觉特征提取器（基于R2Gen的设计）\n", "    \n", "    功能：\n", "    1. 使用预训练的ResNet101提取特征\n", "    2. 移除最后的分类层\n", "    3. 提取空间特征图和全局特征\n", "    \"\"\"\n", "    \n", "    def __init__(self, pretrained=True):\n", "        super(SimplifiedVisualExtractor, self).__init__()\n", "        \n", "        # 加载预训练的ResNet101\n", "        resnet = models.resnet101(pretrained=pretrained)\n", "        \n", "        # 移除最后的全连接层和全局平均池化层\n", "        modules = list(resnet.children())[:-2]  # 移除avgpool和fc\n", "        self.model = nn.Sequential(*modules)\n", "        \n", "        # 全局平均池化（用于生成全局特征）\n", "        self.avg_pool = nn.AdaptiveAvgPool2d((1, 1))\n", "        \n", "        print(f\"视觉特征提取器初始化完成\")\n", "        print(f\"使用预训练权重: {pretrained}\")\n", "    \n", "    def forward(self, images):\n", "        \"\"\"\n", "        前向传播\n", "        \n", "        Args:\n", "            images (torch.Tensor): 输入图像 (batch_size, 3, 224, 224)\n", "        \n", "        Returns:\n", "            tuple: (patch_features, global_features)\n", "                - patch_features: 空间特征图 (batch_size, 2048, 7, 7)\n", "                - global_features: 全局特征 (batch_size, 2048)\n", "        \"\"\"\n", "        # 提取特征图\n", "        patch_features = self.model(images)  # (batch_size, 2048, 7, 7)\n", "        \n", "        # 全局平均池化得到全局特征\n", "        global_features = self.avg_pool(patch_features)  # (batch_size, 2048, 1, 1)\n", "        global_features = global_features.squeeze(-1).squeeze(-1)  # (batch_size, 2048)\n", "        \n", "        # 重新整理patch_features的维度以便后续处理\n", "        batch_size, feat_size, h, w = patch_features.shape\n", "        patch_features = patch_features.reshape(batch_size, feat_size, -1)  # (batch_size, 2048, 49)\n", "        patch_features = patch_features.permute(0, 2, 1)  # (batch_size, 49, 2048)\n", "        \n", "        return patch_features, global_features\n", "\n", "# 创建视觉特征提取器\n", "if r2gen_available:\n", "    # 使用R2Gen的原始实现\n", "    class Args:\n", "        visual_extractor = 'resnet101'\n", "        visual_extractor_pretrained = True\n", "    \n", "    args = Args()\n", "    visual_extractor = VisualExtractor(args)\n", "    print(\"使用R2Gen原始视觉特征提取器\")\nelse:\n", "    # 使用简化版本\n", "    visual_extractor = SimplifiedVisualExtractor(pretrained=True)\n", "    print(\"使用简化版视觉特征提取器\")\n", "\n", "visual_extractor.to(device)\n", "visual_extractor.eval()  # 设置为评估模式\n", "\n", "print(f\"\\n模型参数数量: {sum(p.numel() for p in visual_extractor.parameters()):,}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 特征提取演示\n", "\n", "让我们使用实际的医学影像来演示特征提取过程："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def extract_and_visualize_features(image_path, visual_extractor, device):\n", "    \"\"\"\n", "    提取并可视化图像特征\n", "    \n", "    Args:\n", "        image_path (str): 图像路径\n", "        visual_extractor: 视觉特征提取器\n", "        device: 计算设备\n", "    \"\"\"\n", "    # 图像预处理\n", "    transform = transforms.Compose([\n", "        transforms.Resize((224, 224)),\n", "        transforms.To<PERSON><PERSON><PERSON>(),\n", "        transforms.Normalize(mean=[0.485, 0.456, 0.406], \n", "                           std=[0.229, 0.224, 0.225])\n", "    ])\n", "    \n", "    try:\n", "        # 加载和预处理图像\n", "        image = Image.open(image_path).convert('RGB')\n", "        input_tensor = transform(image).unsqueeze(0).to(device)\n", "        \n", "        print(f\"输入图像形状: {input_tensor.shape}\")\n", "        \n", "        # 提取特征\n", "        with torch.no_grad():\n", "            if r2gen_available:\n", "                patch_features, global_features = visual_extractor(input_tensor)\n", "            else:\n", "                patch_features, global_features = visual_extractor(input_tensor)\n", "        \n", "        print(f\"空间特征形状: {patch_features.shape}\")\n", "        print(f\"全局特征形状: {global_features.shape}\")\n", "        \n", "        # 可视化\n", "        fig, axes = plt.subplots(2, 3, figsize=(15, 10))\n", "        \n", "        # 原始图像\n", "        axes[0, 0].imshow(image)\n", "        axes[0, 0].set_title('原始图像')\n", "        axes[0, 0].axis('off')\n", "        \n", "        # 预处理后的图像（反标准化用于显示）\n", "        mean = torch.tensor([0.485, 0.456, 0.406]).view(3, 1, 1)\n", "        std = torch.tensor([0.229, 0.224, 0.225]).view(3, 1, 1)\n", "        denorm_img = input_tensor.cpu().squeeze(0) * std + mean\n", "        denorm_img = torch.clamp(denorm_img, 0, 1)\n", "        \n", "        axes[0, 1].imshow(denorm_img.permute(1, 2, 0))\n", "        axes[0, 1].set_title('预处理后图像')\n", "        axes[0, 1].axis('off')\n", "        \n", "        # 全局特征可视化（前100个维度）\n", "        axes[0, 2].bar(range(100), global_features.cpu().numpy()[0][:100])\n", "        axes[0, 2].set_title('全局特征 (前100维)')\n", "        axes[0, 2].set_xlabel('特征维度')\n", "        axes[0, 2].set_ylabel('特征值')\n", "        \n", "        # 空间特征可视化（选择几个有代表性的特征图）\n", "        if not r2gen_available:\n", "            # 对于简化版本，需要重新获取7x7的特征图\n", "            with torch.no_grad():\n", "                spatial_features = visual_extractor.model(input_tensor)  # (1, 2048, 7, 7)\n", "        else:\n", "            # R2Gen版本需要重新整理维度\n", "            spatial_features = patch_features.permute(0, 2, 1).reshape(1, 2048, 7, 7)\n", "        \n", "        # 显示几个特征图\n", "        feature_indices = [0, 512, 1024, 1536]  # 选择不同的特征通道\n", "        for i, idx in enumerate(feature_indices[:3]):\n", "            if idx < spatial_features.shape[1]:\n", "                feature_map = spatial_features[0, idx].cpu().numpy()\n", "                axes[1, i].imshow(feature_map, cmap='viridis')\n", "                axes[1, i].set_title(f'特征图 {idx}')\n", "                axes[1, i].axis('off')\n", "        \n", "        plt.tight_layout()\n", "        plt.show()\n", "        \n", "        return patch_features, global_features\n", "        \n", "    except Exception as e:\n", "        print(f\"处理图像时出错: {e}\")\n", "        return None, None\n", "\n", "# 尝试加载示例图像\n", "annotation_path = os.path.join(project_root, 'data', 'iu_xray', 'annotation.json')\n", "image_dir = os.path.join(project_root, 'data', 'iu_xray', 'images')\n", "\n", "sample_image_path = None\n", "try:\n", "    with open(annotation_path, 'r') as f:\n", "        data = json.load(f)\n", "    \n", "    # 找到第一个存在的图像文件\n", "    for sample in data['train'][:5]:\n", "        for img_path in sample['image_path']:\n", "            full_path = os.path.join(image_dir, img_path)\n", "            if os.path.exists(full_path):\n", "                sample_image_path = full_path\n", "                break\n", "        if sample_image_path:\n", "            break\n", "            \n", "except FileNotFoundError:\n", "    print(\"数据集文件未找到\")\n", "\n", "if sample_image_path:\n", "    print(f\"使用示例图像: {sample_image_path}\")\n", "    patch_features, global_features = extract_and_visualize_features(\n", "        sample_image_path, visual_extractor, device\n", "    )\nelse:\n", "    print(\"未找到可用的示例图像，使用随机图像演示\")\n", "    # 创建随机图像进行演示\n", "    random_image = torch.randn(1, 3, 224, 224).to(device)\n", "    \n", "    with torch.no_grad():\n", "        if r2gen_available:\n", "            patch_features, global_features = visual_extractor(random_image)\n", "        else:\n", "            patch_features, global_features = visual_extractor(random_image)\n", "    \n", "    print(f\"随机图像特征提取结果:\")\n", "    print(f\"空间特征形状: {patch_features.shape}\")\n", "    print(f\"全局特征形状: {global_features.shape}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 特征分析和理解\n", "\n", "让我们深入分析提取的特征，理解它们的含义："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def analyze_features(patch_features, global_features):\n", "    \"\"\"\n", "    分析提取的特征\n", "    \n", "    Args:\n", "        patch_features (torch.Tensor): 空间特征 (batch_size, num_patches, feature_dim)\n", "        global_features (torch.Tensor): 全局特征 (batch_size, feature_dim)\n", "    \"\"\"\n", "    if patch_features is None or global_features is None:\n", "        print(\"特征为空，跳过分析\")\n", "        return\n", "    \n", "    print(\"=== 特征分析 ===\")\n", "    \n", "    # 转换为numpy数组便于分析\n", "    patch_np = patch_features.cpu().numpy()[0]  # (num_patches, feature_dim)\n", "    global_np = global_features.cpu().numpy()[0]  # (feature_dim,)\n", "    \n", "    print(f\"空间特征维度: {patch_np.shape}\")\n", "    print(f\"全局特征维度: {global_np.shape}\")\n", "    \n", "    # 统计信息\n", "    print(f\"\\n空间特征统计:\")\n", "    print(f\"  均值: {patch_np.mean():.4f}\")\n", "    print(f\"  标准差: {patch_np.std():.4f}\")\n", "    print(f\"  最小值: {patch_np.min():.4f}\")\n", "    print(f\"  最大值: {patch_np.max():.4f}\")\n", "    \n", "    print(f\"\\n全局特征统计:\")\n", "    print(f\"  均值: {global_np.mean():.4f}\")\n", "    print(f\"  标准差: {global_np.std():.4f}\")\n", "    print(f\"  最小值: {global_np.min():.4f}\")\n", "    print(f\"  最大值: {global_np.max():.4f}\")\n", "    \n", "    # 可视化特征分布\n", "    fig, axes = plt.subplots(2, 2, figsize=(12, 8))\n", "    \n", "    # 空间特征分布\n", "    axes[0, 0].hist(patch_np.flatten(), bins=50, alpha=0.7, edgecolor='black')\n", "    axes[0, 0].set_title('空间特征值分布')\n", "    axes[0, 0].set_xlabel('特征值')\n", "    axes[0, 0].set_ylabel('频次')\n", "    \n", "    # 全局特征分布\n", "    axes[0, 1].hist(global_np, bins=50, alpha=0.7, edgecolor='black', color='orange')\n", "    axes[0, 1].set_title('全局特征值分布')\n", "    axes[0, 1].set_xlabel('特征值')\n", "    axes[0, 1].set_ylabel('频次')\n", "    \n", "    # 空间特征的空间分布（每个patch的平均激活）\n", "    patch_means = patch_np.mean(axis=1)  # 每个patch的平均特征值\n", "    patch_means_2d = patch_means.reshape(7, 7)  # 重新整理为7x7的空间布局\n", "    \n", "    im = axes[1, 0].imshow(patch_means_2d, cmap='viridis')\n", "    axes[1, 0].set_title('空间特征激活强度')\n", "    axes[1, 0].set_xlabel('空间位置 X')\n", "    axes[1, 0].set_ylabel('空间位置 Y')\n", "    plt.colorbar(im, ax=axes[1, 0])\n", "    \n", "    # 特征维度的重要性（方差分析）\n", "    feature_vars = patch_np.var(axis=0)  # 每个特征维度在空间上的方差\n", "    top_features = np.argsort(feature_vars)[-20:]  # 方差最大的20个特征\n", "    \n", "    axes[1, 1].bar(range(20), feature_vars[top_features])\n", "    axes[1, 1].set_title('最重要的20个特征维度')\n", "    axes[1, 1].set_xlabel('特征维度排名')\n", "    axes[1, 1].set_ylabel('空间方差')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # 计算特征的稀疏性\n", "    patch_sparsity = (patch_np == 0).mean()\n", "    global_sparsity = (global_np == 0).mean()\n", "    \n", "    print(f\"\\n特征稀疏性:\")\n", "    print(f\"  空间特征零值比例: {patch_sparsity:.2%}\")\n", "    print(f\"  全局特征零值比例: {global_sparsity:.2%}\")\n", "    \n", "    return patch_means_2d, top_features\n", "\n", "# 分析特征\n", "if 'patch_features' in locals() and 'global_features' in locals():\n", "    spatial_activation, important_features = analyze_features(patch_features, global_features)\nelse:\n", "    print(\"没有可用的特征进行分析\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 预训练模型的重要性\n", "\n", "让我们比较使用预训练权重和随机初始化权重的差异："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def compare_pretrained_vs_random():\n", "    \"\"\"\n", "    比较预训练模型和随机初始化模型的特征提取能力\n", "    \"\"\"\n", "    print(\"=== 预训练 vs 随机初始化比较 ===\")\n", "    \n", "    # 创建两个模型：预训练和随机初始化\n", "    pretrained_model = SimplifiedVisualExtractor(pretrained=True).to(device)\n", "    random_model = SimplifiedVisualExtractor(pretrained=False).to(device)\n", "    \n", "    pretrained_model.eval()\n", "    random_model.eval()\n", "    \n", "    # 创建测试图像\n", "    test_image = torch.randn(1, 3, 224, 224).to(device)\n", "    \n", "    with torch.no_grad():\n", "        # 预训练模型特征\n", "        pre_patch, pre_global = pretrained_model(test_image)\n", "        \n", "        # 随机初始化模型特征\n", "        rand_patch, rand_global = random_model(test_image)\n", "    \n", "    # 转换为numpy进行分析\n", "    pre_patch_np = pre_patch.cpu().numpy()[0]\n", "    pre_global_np = pre_global.cpu().numpy()[0]\n", "    rand_patch_np = rand_patch.cpu().numpy()[0]\n", "    rand_global_np = rand_global.cpu().numpy()[0]\n", "    \n", "    # 比较统计信息\n", "    print(f\"\\n预训练模型:\")\n", "    print(f\"  空间特征 - 均值: {pre_patch_np.mean():.4f}, 标准差: {pre_patch_np.std():.4f}\")\n", "    print(f\"  全局特征 - 均值: {pre_global_np.mean():.4f}, 标准差: {pre_global_np.std():.4f}\")\n", "    \n", "    print(f\"\\n随机初始化模型:\")\n", "    print(f\"  空间特征 - 均值: {rand_patch_np.mean():.4f}, 标准差: {rand_patch_np.std():.4f}\")\n", "    print(f\"  全局特征 - 均值: {rand_global_np.mean():.4f}, 标准差: {rand_global_np.std():.4f}\")\n", "    \n", "    # 可视化比较\n", "    fig, axes = plt.subplots(2, 3, figsize=(15, 8))\n", "    \n", "    # 预训练模型特征分布\n", "    axes[0, 0].hist(pre_patch_np.flatten(), bins=50, alpha=0.7, label='预训练')\n", "    axes[0, 0].hist(rand_patch_np.flatten(), bins=50, alpha=0.7, label='随机初始化')\n", "    axes[0, 0].set_title('空间特征分布比较')\n", "    axes[0, 0].legend()\n", "    \n", "    axes[0, 1].hist(pre_global_np, bins=50, alpha=0.7, label='预训练')\n", "    axes[0, 1].hist(rand_global_np, bins=50, alpha=0.7, label='随机初始化')\n", "    axes[0, 1].set_title('全局特征分布比较')\n", "    axes[0, 1].legend()\n", "    \n", "    # 空间激活模式比较\n", "    pre_spatial = pre_patch_np.mean(axis=1).reshape(7, 7)\n", "    rand_spatial = rand_patch_np.mean(axis=1).reshape(7, 7)\n", "    \n", "    im1 = axes[0, 2].imshow(pre_spatial, cmap='viridis')\n", "    axes[0, 2].set_title('预训练模型空间激活')\n", "    plt.colorbar(im1, ax=axes[0, 2])\n", "    \n", "    im2 = axes[1, 0].imshow(rand_spatial, cmap='viridis')\n", "    axes[1, 0].set_title('随机初始化模型空间激活')\n", "    plt.colorbar(im2, ax=axes[1, 0])\n", "    \n", "    # 特征相关性分析\n", "    correlation = np.corrcoef(pre_global_np, rand_global_np)[0, 1]\n", "    axes[1, 1].scatter(pre_global_np[:100], rand_global_np[:100], alpha=0.6)\n", "    axes[1, 1].set_xlabel('预训练模型特征')\n", "    axes[1, 1].set_ylabel('随机初始化模型特征')\n", "    axes[1, 1].set_title(f'特征相关性 (r={correlation:.3f})')\n", "    \n", "    # 特征激活强度比较\n", "    pre_activation = np.abs(pre_global_np).mean()\n", "    rand_activation = np.abs(rand_global_np).mean()\n", "    \n", "    axes[1, 2].bar(['预训练', '随机初始化'], [pre_activation, rand_activation])\n", "    axes[1, 2].set_title('平均激活强度')\n", "    axes[1, 2].set_ylabel('|特征值|的均值')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    print(f\"\\n关键观察:\")\n", "    print(f\"1. 预训练模型的特征更加结构化和有意义\")\n", "    print(f\"2. 随机初始化模型的特征分布更加随机\")\n", "    print(f\"3. 预训练模型的空间激活模式更有规律\")\n", "    print(f\"4. 特征相关性: {correlation:.3f} (接近0表示两者差异很大)\")\n", "\n", "compare_pretrained_vs_random()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 小结\n", "\n", "在本节中，我们深入学习了CNN特征提取器的工作原理：\n", "\n", "### 核心概念\n", "\n", "1. **卷积操作**：\n", "   - 局部连接和权重共享\n", "   - 不同卷积核检测不同类型的特征\n", "   - 通过多层卷积构建层次化特征表示\n", "\n", "2. **残差连接**：\n", "   - 解决深度网络的梯度消失问题\n", "   - 允许训练更深的网络\n", "   - 学习残差函数而非直接映射\n", "\n", "3. **特征提取**：\n", "   - **空间特征**：保留位置信息的特征图\n", "   - **全局特征**：整个图像的抽象表示\n", "   - 特征维度通常为2048（ResNet101）\n", "\n", "### 实践要点\n", "\n", "1. **预训练的重要性**：\n", "   - 预训练模型提供更有意义的特征\n", "   - 加速训练收敛\n", "   - 提高最终性能\n", "\n", "2. **特征分析**：\n", "   - 观察特征分布和激活模式\n", "   - 理解不同特征维度的重要性\n", "   - 监控特征的稀疏性和多样性\n", "\n", "3. **医学影像特点**：\n", "   - 需要关注细微的病理特征\n", "   - 空间位置信息很重要\n", "   - 预训练模型需要适应医学领域\n", "\n", "### 下一步\n", "\n", "在下一个notebook中，我们将学习Transformer编码器-解码器架构，了解如何将视觉特征转换为自然语言描述。这是连接图像理解和文本生成的关键桥梁。\n", "\n", "### 关键收获\n", "\n", "- CNN通过层次化特征学习实现图像理解\n", "- 残差连接是训练深度网络的关键技术\n", "- 预训练模型提供了强大的特征表示能力\n", "- 特征分析帮助我们理解模型的工作机制\n", "- 医学影像需要特别关注空间特征和细节信息"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}