{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 08 - 训练流程实现\n", "\n", "在这个notebook中，我们将实现完整的深度学习训练流程，包括数据加载、模型训练、验证、保存和恢复等所有关键组件。\n", "\n", "## 学习目标\n", "\n", "- 理解完整的训练流程架构\n", "- 实现训练和验证循环\n", "- 学习模型保存和加载机制\n", "- 掌握训练监控和日志记录\n", "- 实现早停和检查点机制\n", "- 理解分布式训练基础\n", "- 实现完整的R2Gen训练流程\n", "\n", "## 训练流程概述\n", "\n", "一个完整的深度学习训练流程通常包括：\n", "\n", "1. **数据准备**：加载和预处理数据\n", "2. **模型初始化**：创建模型、优化器、损失函数\n", "3. **训练循环**：前向传播、反向传播、参数更新\n", "4. **验证评估**：在验证集上评估模型性能\n", "5. **模型保存**：保存最佳模型和检查点\n", "6. **监控记录**：记录训练过程和指标\n", "7. **早停机制**：防止过拟合\n", "\n", "### 训练流程的挑战\n", "\n", "- **内存管理**：处理大型模型和数据集\n", "- **训练稳定性**：避免梯度爆炸、消失等问题\n", "- **超参数调优**：找到最佳的训练配置\n", "- **可重现性**：确保实验结果可重现\n", "- **效率优化**：充分利用计算资源"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 导入必要的库\n", "import os\n", "import sys\n", "import json\n", "import time\n", "import logging\n", "from datetime import datetime\n", "from pathlib import Path\n", "import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "from torch.utils.data import DataLoader\n", "from torch.utils.tensorboard import SummaryWriter\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from tqdm import tqdm\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# 设置项目路径\n", "project_root = os.path.abspath('..')\n", "sys.path.append(project_root)\n", "\n", "print(f\"项目根目录: {project_root}\")\n", "print(f\"PyTorch版本: {torch.__version__}\")\n", "\n", "# 设置设备\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f\"使用设备: {device}\")\n", "if torch.cuda.is_available():\n", "    print(f\"GPU数量: {torch.cuda.device_count()}\")\n", "    print(f\"当前GPU: {torch.cuda.get_device_name(0)}\")\n", "\n", "# 设置随机种子以确保可重现性\n", "def set_seed(seed=42):\n", "    \"\"\"设置随机种子以确保实验可重现\"\"\"\n", "    torch.manual_seed(seed)\n", "    torch.cuda.manual_seed(seed)\n", "    torch.cuda.manual_seed_all(seed)\n", "    np.random.seed(seed)\n", "    torch.backends.cudnn.deterministic = True\n", "    torch.backends.cudnn.benchmark = False\n", "\n", "set_seed(42)\n", "print(\"随机种子已设置为42\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 训练配置管理\n", "\n", "首先，我们需要一个配置管理系统来管理所有的训练参数："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class TrainingConfig:\n", "    \"\"\"\n", "    训练配置类\n", "    \n", "    管理所有训练相关的超参数和设置\n", "    \"\"\"\n", "    \n", "    def __init__(self, config_dict=None):\n", "        # 默认配置\n", "        self.default_config = {\n", "            # 模型参数\n", "            'model_name': 'r2gen',\n", "            'vocab_size': 1000,\n", "            'd_model': 512,\n", "            'num_heads': 8,\n", "            'num_layers': 6,\n", "            'd_ff': 2048,\n", "            'max_seq_length': 60,\n", "            'dropout': 0.1,\n", "            \n", "            # 训练参数\n", "            'batch_size': 16,\n", "            'num_epochs': 100,\n", "            'learning_rate': 1e-4,\n", "            'weight_decay': 1e-5,\n", "            'warmup_epochs': 5,\n", "            'grad_clip_norm': 1.0,\n", "            \n", "            # 优化器设置\n", "            'optimizer': 'adamw',\n", "            'scheduler': 'cosine',\n", "            'label_smoothing': 0.1,\n", "            \n", "            # 数据设置\n", "            'num_workers': 4,\n", "            'pin_memory': True,\n", "            'pad_idx': 0,\n", "            \n", "            # 保存和日志\n", "            'save_dir': './checkpoints',\n", "            'log_dir': './logs',\n", "            'save_every': 10,\n", "            'log_every': 100,\n", "            'eval_every': 1,\n", "            \n", "            # 早停\n", "            'early_stopping': True,\n", "            'patience': 10,\n", "            'min_delta': 1e-4,\n", "            \n", "            # 其他\n", "            'resume_from': None,\n", "            'seed': 42\n", "        }\n", "        \n", "        # 更新配置\n", "        if config_dict:\n", "            self.default_config.update(config_dict)\n", "        \n", "        # 将配置项设置为属性\n", "        for key, value in self.default_config.items():\n", "            setattr(self, key, value)\n", "    \n", "    def save(self, filepath):\n", "        \"\"\"保存配置到文件\"\"\"\n", "        with open(filepath, 'w') as f:\n", "            json.dump(self.default_config, f, indent=2)\n", "    \n", "    @classmethod\n", "    def load(cls, filepath):\n", "        \"\"\"从文件加载配置\"\"\"\n", "        with open(filepath, 'r') as f:\n", "            config_dict = json.load(f)\n", "        return cls(config_dict)\n", "    \n", "    def __str__(self):\n", "        \"\"\"打印配置信息\"\"\"\n", "        config_str = \"Training Configuration:\\n\"\n", "        config_str += \"=\" * 50 + \"\\n\"\n", "        for key, value in self.default_config.items():\n", "            config_str += f\"{key:20s}: {value}\\n\"\n", "        config_str += \"=\" * 50\n", "        return config_str\n", "\n", "# 创建示例配置\n", "config = TrainingConfig({\n", "    'batch_size': 8,  # 使用较小的批次大小用于演示\n", "    'num_epochs': 20,\n", "    'learning_rate': 5e-4\n", "})\n", "\n", "print(config)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 训练器类实现\n", "\n", "现在我们实现一个完整的训练器类，封装所有训练相关的功能："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class Trainer:\n", "    \"\"\"\n", "    完整的训练器类\n", "    \n", "    封装训练、验证、保存、加载等所有功能\n", "    \"\"\"\n", "    \n", "    def __init__(self, model, train_loader, val_loader, config):\n", "        self.model = model\n", "        self.train_loader = train_loader\n", "        self.val_loader = val_loader\n", "        self.config = config\n", "        self.device = device\n", "        \n", "        # 将模型移到设备\n", "        self.model.to(self.device)\n", "        \n", "        # 设置损失函数\n", "        self.criterion = nn.CrossEntropyLoss(\n", "            ignore_index=config.pad_idx,\n", "            label_smoothing=config.label_smoothing\n", "        )\n", "        \n", "        # 设置优化器\n", "        self._setup_optimizer()\n", "        \n", "        # 设置学习率调度器\n", "        self._setup_scheduler()\n", "        \n", "        # 创建保存目录\n", "        self.save_dir = Path(config.save_dir)\n", "        self.save_dir.mkdir(parents=True, exist_ok=True)\n", "        \n", "        # 设置日志\n", "        self._setup_logging()\n", "        \n", "        # 训练状态\n", "        self.current_epoch = 0\n", "        self.best_val_loss = float('inf')\n", "        self.patience_counter = 0\n", "        self.training_history = {\n", "            'train_loss': [],\n", "            'val_loss': [],\n", "            'train_acc': [],\n", "            'val_acc': [],\n", "            'learning_rates': []\n", "        }\n", "        \n", "        print(f\"训练器初始化完成\")\n", "        print(f\"模型参数数量: {sum(p.numel() for p in model.parameters()):,}\")\n", "        print(f\"可训练参数数量: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}\")\n", "    \n", "    def _setup_optimizer(self):\n", "        \"\"\"设置优化器\"\"\"\n", "        if self.config.optimizer.lower() == 'adam':\n", "            self.optimizer = optim.<PERSON>(\n", "                self.model.parameters(),\n", "                lr=self.config.learning_rate,\n", "                weight_decay=self.config.weight_decay\n", "            )\n", "        elif self.config.optimizer.lower() == 'adamw':\n", "            self.optimizer = optim.AdamW(\n", "                self.model.parameters(),\n", "                lr=self.config.learning_rate,\n", "                weight_decay=self.config.weight_decay\n", "            )\n", "        else:\n", "            raise ValueError(f\"不支持的优化器: {self.config.optimizer}\")\n", "    \n", "    def _setup_scheduler(self):\n", "        \"\"\"设置学习率调度器\"\"\"\n", "        if self.config.scheduler == 'cosine':\n", "            self.scheduler = optim.lr_scheduler.CosineAnnealingLR(\n", "                self.optimizer, T_max=self.config.num_epochs\n", "            )\n", "        elif self.config.scheduler == 'step':\n", "            self.scheduler = optim.lr_scheduler.StepLR(\n", "                self.optimizer, step_size=30, gamma=0.5\n", "            )\n", "        elif self.config.scheduler == 'plateau':\n", "            self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(\n", "                self.optimizer, mode='min', patience=5, factor=0.5\n", "            )\n", "        else:\n", "            self.scheduler = None\n", "    \n", "    def _setup_logging(self):\n", "        \"\"\"设置日志记录\"\"\"\n", "        # 创建日志目录\n", "        log_dir = Path(self.config.log_dir)\n", "        log_dir.mkdir(parents=True, exist_ok=True)\n", "        \n", "        # 设置文件日志\n", "        log_file = log_dir / f\"training_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log\"\n", "        logging.basicConfig(\n", "            level=logging.INFO,\n", "            format='%(asctime)s - %(levelname)s - %(message)s',\n", "            handlers=[\n", "                logging.FileHandler(log_file),\n", "                logging.StreamHandler()\n", "            ]\n", "        )\n", "        self.logger = logging.getLogger(__name__)\n", "        \n", "        # 设置TensorBoard（如果可用）\n", "        try:\n", "            tensorboard_dir = log_dir / \"tensorboard\"\n", "            self.writer = SummaryWriter(tensorboard_dir)\n", "            self.use_tensorboard = True\n", "        except:\n", "            self.writer = None\n", "            self.use_tensorboard = False\n", "            self.logger.warning(\"TensorBoard不可用，跳过\")\n", "    \n", "    def train_epoch(self):\n", "        \"\"\"\n", "        训练一个epoch\n", "        \n", "        Returns:\n", "            tuple: (平均损失, 平均准确率)\n", "        \"\"\"\n", "        self.model.train()\n", "        total_loss = 0.0\n", "        total_correct = 0\n", "        total_tokens = 0\n", "        \n", "        # 使用tqdm显示进度条\n", "        pbar = tqdm(self.train_loader, desc=f\"Epoch {self.current_epoch+1} [Train]\")\n", "        \n", "        for batch_idx, batch in enumerate(pbar):\n", "            # 数据移到设备\n", "            if isinstance(batch, (list, tuple)):\n", "                batch = [item.to(self.device) if torch.is_tensor(item) else item for item in batch]\n", "                inputs, targets = batch[0], batch[1]\n", "                mask = batch[2] if len(batch) > 2 else None\n", "            else:\n", "                inputs = batch.to(self.device)\n", "                targets = inputs  # 自回归任务\n", "                mask = None\n", "            \n", "            # 前向传播\n", "            self.optimizer.zero_grad()\n", "            outputs = self.model(inputs)\n", "            \n", "            # 计算损失\n", "            if outputs.dim() == 3:  # (batch_size, seq_len, vocab_size)\n", "                outputs = outputs.view(-1, outputs.size(-1))\n", "                targets = targets.view(-1)\n", "            \n", "            loss = self.criterion(outputs, targets)\n", "            \n", "            # 反向传播\n", "            loss.backward()\n", "            \n", "            # 梯度裁剪\n", "            if self.config.grad_clip_norm > 0:\n", "                torch.nn.utils.clip_grad_norm_(\n", "                    self.model.parameters(), \n", "                    self.config.grad_clip_norm\n", "                )\n", "            \n", "            # 优化器步骤\n", "            self.optimizer.step()\n", "            \n", "            # 统计\n", "            total_loss += loss.item()\n", "            \n", "            # 计算准确率\n", "            with torch.no_grad():\n", "                predictions = outputs.argmax(dim=-1)\n", "                if mask is not None:\n", "                    mask_flat = mask.view(-1).bool()\n", "                    correct = (predictions == targets) & mask_flat\n", "                    total_correct += correct.sum().item()\n", "                    total_tokens += mask_flat.sum().item()\n", "                else:\n", "                    non_pad_mask = (targets != self.config.pad_idx)\n", "                    correct = (predictions == targets) & non_pad_mask\n", "                    total_correct += correct.sum().item()\n", "                    total_tokens += non_pad_mask.sum().item()\n", "            \n", "            # 更新进度条\n", "            pbar.set_postfix({\n", "                'loss': f'{loss.item():.4f}',\n", "                'lr': f'{self.optimizer.param_groups[0][\"lr\"]:.6f}'\n", "            })\n", "            \n", "            # 记录到TensorBoard\n", "            if self.use_tensorboard and batch_idx % self.config.log_every == 0:\n", "                global_step = self.current_epoch * len(self.train_loader) + batch_idx\n", "                self.writer.add_scalar('Train/BatchLoss', loss.item(), global_step)\n", "                self.writer.add_scalar('Train/LearningRate', \n", "                                     self.optimizer.param_groups[0]['lr'], global_step)\n", "        \n", "        avg_loss = total_loss / len(self.train_loader)\n", "        avg_acc = total_correct / total_tokens if total_tokens > 0 else 0.0\n", "        \n", "        return avg_loss, avg_acc"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["    def validate_epoch(self):\n", "        \"\"\"\n", "        验证一个epoch\n", "        \n", "        Returns:\n", "            tuple: (平均损失, 平均准确率)\n", "        \"\"\"\n", "        self.model.eval()\n", "        total_loss = 0.0\n", "        total_correct = 0\n", "        total_tokens = 0\n", "        \n", "        pbar = tqdm(self.val_loader, desc=f\"Epoch {self.current_epoch+1} [Val]\")\n", "        \n", "        with torch.no_grad():\n", "            for batch in pbar:\n", "                # 数据移到设备\n", "                if isinstance(batch, (list, tuple)):\n", "                    batch = [item.to(self.device) if torch.is_tensor(item) else item for item in batch]\n", "                    inputs, targets = batch[0], batch[1]\n", "                    mask = batch[2] if len(batch) > 2 else None\n", "                else:\n", "                    inputs = batch.to(self.device)\n", "                    targets = inputs\n", "                    mask = None\n", "                \n", "                # 前向传播\n", "                outputs = self.model(inputs)\n", "                \n", "                # 计算损失\n", "                if outputs.dim() == 3:\n", "                    outputs = outputs.view(-1, outputs.size(-1))\n", "                    targets = targets.view(-1)\n", "                \n", "                loss = self.criterion(outputs, targets)\n", "                total_loss += loss.item()\n", "                \n", "                # 计算准确率\n", "                predictions = outputs.argmax(dim=-1)\n", "                if mask is not None:\n", "                    mask_flat = mask.view(-1).bool()\n", "                    correct = (predictions == targets) & mask_flat\n", "                    total_correct += correct.sum().item()\n", "                    total_tokens += mask_flat.sum().item()\n", "                else:\n", "                    non_pad_mask = (targets != self.config.pad_idx)\n", "                    correct = (predictions == targets) & non_pad_mask\n", "                    total_correct += correct.sum().item()\n", "                    total_tokens += non_pad_mask.sum().item()\n", "                \n", "                pbar.set_postfix({'loss': f'{loss.item():.4f}'})\n", "        \n", "        avg_loss = total_loss / len(self.val_loader)\n", "        avg_acc = total_correct / total_tokens if total_tokens > 0 else 0.0\n", "        \n", "        return avg_loss, avg_acc\n", "    \n", "    def save_checkpoint(self, is_best=False, filename=None):\n", "        \"\"\"\n", "        保存模型检查点\n", "        \n", "        Args:\n", "            is_best (bool): 是否是最佳模型\n", "            filename (str): 自定义文件名\n", "        \"\"\"\n", "        if filename is None:\n", "            filename = f\"checkpoint_epoch_{self.current_epoch}.pth\"\n", "        \n", "        checkpoint = {\n", "            'epoch': self.current_epoch,\n", "            'model_state_dict': self.model.state_dict(),\n", "            'optimizer_state_dict': self.optimizer.state_dict(),\n", "            'scheduler_state_dict': self.scheduler.state_dict() if self.scheduler else None,\n", "            'best_val_loss': self.best_val_loss,\n", "            'training_history': self.training_history,\n", "            'config': self.config.default_config\n", "        }\n", "        \n", "        # 保存检查点\n", "        checkpoint_path = self.save_dir / filename\n", "        torch.save(checkpoint, checkpoint_path)\n", "        self.logger.info(f\"检查点已保存: {checkpoint_path}\")\n", "        \n", "        # 如果是最佳模型，额外保存一份\n", "        if is_best:\n", "            best_path = self.save_dir / \"best_model.pth\"\n", "            torch.save(checkpoint, best_path)\n", "            self.logger.info(f\"最佳模型已保存: {best_path}\")\n", "    \n", "    def load_checkpoint(self, checkpoint_path):\n", "        \"\"\"\n", "        加载模型检查点\n", "        \n", "        Args:\n", "            checkpoint_path (str): 检查点文件路径\n", "        \"\"\"\n", "        checkpoint = torch.load(checkpoint_path, map_location=self.device)\n", "        \n", "        # 加载模型状态\n", "        self.model.load_state_dict(checkpoint['model_state_dict'])\n", "        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])\n", "        \n", "        if self.scheduler and checkpoint['scheduler_state_dict']:\n", "            self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])\n", "        \n", "        # 恢复训练状态\n", "        self.current_epoch = checkpoint['epoch']\n", "        self.best_val_loss = checkpoint['best_val_loss']\n", "        self.training_history = checkpoint['training_history']\n", "        \n", "        self.logger.info(f\"检查点已加载: {checkpoint_path}\")\n", "        self.logger.info(f\"从epoch {self.current_epoch} 恢复训练\")\n", "    \n", "    def early_stopping_check(self, val_loss):\n", "        \"\"\"\n", "        检查是否应该早停\n", "        \n", "        Args:\n", "            val_loss (float): 当前验证损失\n", "        \n", "        Returns:\n", "            bool: 是否应该早停\n", "        \"\"\"\n", "        if not self.config.early_stopping:\n", "            return False\n", "        \n", "        if val_loss < self.best_val_loss - self.config.min_delta:\n", "            self.best_val_loss = val_loss\n", "            self.patience_counter = 0\n", "            return False\n", "        else:\n", "            self.patience_counter += 1\n", "            if self.patience_counter >= self.config.patience:\n", "                self.logger.info(f\"早停触发: 验证损失在{self.config.patience}个epoch内未改善\")\n", "                return True\n", "            return False"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["    def train(self):\n", "        \"\"\"\n", "        完整的训练流程\n", "        \n", "        Returns:\n", "            dict: 训练历史\n", "        \"\"\"\n", "        self.logger.info(\"开始训练...\")\n", "        self.logger.info(f\"训练配置:\\n{self.config}\")\n", "        \n", "        # 如果指定了恢复检查点，则加载\n", "        if self.config.resume_from:\n", "            self.load_checkpoint(self.config.resume_from)\n", "        \n", "        start_time = time.time()\n", "        \n", "        try:\n", "            for epoch in range(self.current_epoch, self.config.num_epochs):\n", "                self.current_epoch = epoch\n", "                epoch_start_time = time.time()\n", "                \n", "                # 训练阶段\n", "                train_loss, train_acc = self.train_epoch()\n", "                \n", "                # 验证阶段\n", "                if epoch % self.config.eval_every == 0:\n", "                    val_loss, val_acc = self.validate_epoch()\n", "                else:\n", "                    val_loss, val_acc = float('inf'), 0.0\n", "                \n", "                # 学习率调度\n", "                if self.scheduler:\n", "                    if isinstance(self.scheduler, optim.lr_scheduler.ReduceLROnPlateau):\n", "                        self.scheduler.step(val_loss)\n", "                    else:\n", "                        self.scheduler.step()\n", "                \n", "                # 记录历史\n", "                self.training_history['train_loss'].append(train_loss)\n", "                self.training_history['val_loss'].append(val_loss)\n", "                self.training_history['train_acc'].append(train_acc)\n", "                self.training_history['val_acc'].append(val_acc)\n", "                self.training_history['learning_rates'].append(\n", "                    self.optimizer.param_groups[0]['lr']\n", "                )\n", "                \n", "                # 记录到TensorBoard\n", "                if self.use_tensorboard:\n", "                    self.writer.add_scalar('Train/Loss', train_loss, epoch)\n", "                    self.writer.add_scalar('Train/Accuracy', train_acc, epoch)\n", "                    self.writer.add_scalar('Val/Loss', val_loss, epoch)\n", "                    self.writer.add_scalar('Val/Accuracy', val_acc, epoch)\n", "                    self.writer.add_scalar('Learning_Rate', \n", "                                         self.optimizer.param_groups[0]['lr'], epoch)\n", "                \n", "                # 计算epoch时间\n", "                epoch_time = time.time() - epoch_start_time\n", "                \n", "                # 打印进度\n", "                self.logger.info(\n", "                    f\"Epoch {epoch+1}/{self.config.num_epochs} - \"\n", "                    f\"Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.4f}, \"\n", "                    f\"Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.4f}, \"\n", "                    f\"LR: {self.optimizer.param_groups[0]['lr']:.6f}, \"\n", "                    f\"Time: {epoch_time:.2f}s\"\n", "                )\n", "                \n", "                # 保存检查点\n", "                is_best = val_loss < self.best_val_loss\n", "                if epoch % self.config.save_every == 0 or is_best:\n", "                    self.save_checkpoint(is_best=is_best)\n", "                \n", "                # 早停检查\n", "                if self.early_stopping_check(val_loss):\n", "                    break\n", "        \n", "        except KeyboardInterrupt:\n", "            self.logger.info(\"训练被用户中断\")\n", "            self.save_checkpoint(filename=\"interrupted_checkpoint.pth\")\n", "        \n", "        except Exception as e:\n", "            self.logger.error(f\"训练过程中出现错误: {e}\")\n", "            self.save_checkpoint(filename=\"error_checkpoint.pth\")\n", "            raise\n", "        \n", "        finally:\n", "            # 关闭TensorBoard writer\n", "            if self.use_tensorboard:\n", "                self.writer.close()\n", "        \n", "        total_time = time.time() - start_time\n", "        self.logger.info(f\"训练完成，总用时: {total_time:.2f}秒\")\n", "        \n", "        return self.training_history\n", "    \n", "    def plot_training_history(self):\n", "        \"\"\"\n", "        可视化训练历史\n", "        \"\"\"\n", "        if not self.training_history['train_loss']:\n", "            print(\"没有训练历史可以绘制\")\n", "            return\n", "        \n", "        fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "        \n", "        epochs = range(1, len(self.training_history['train_loss']) + 1)\n", "        \n", "        # 损失曲线\n", "        axes[0, 0].plot(epochs, self.training_history['train_loss'], \n", "                       label='Train Loss', linewidth=2, color='blue')\n", "        axes[0, 0].plot(epochs, self.training_history['val_loss'], \n", "                       label='Val Loss', linewidth=2, color='red')\n", "        axes[0, 0].set_title('训练和验证损失')\n", "        axes[0, 0].set_xlabel('Epoch')\n", "        axes[0, 0].set_ylabel('Loss')\n", "        axes[0, 0].legend()\n", "        axes[0, 0].grid(True, alpha=0.3)\n", "        \n", "        # 准确率曲线\n", "        axes[0, 1].plot(epochs, self.training_history['train_acc'], \n", "                       label='Train Acc', linewidth=2, color='blue')\n", "        axes[0, 1].plot(epochs, self.training_history['val_acc'], \n", "                       label='Val Acc', linewidth=2, color='red')\n", "        axes[0, 1].set_title('训练和验证准确率')\n", "        axes[0, 1].set_xlabel('Epoch')\n", "        axes[0, 1].set_ylabel('Accuracy')\n", "        axes[0, 1].legend()\n", "        axes[0, 1].grid(True, alpha=0.3)\n", "        \n", "        # 学习率变化\n", "        axes[1, 0].plot(epochs, self.training_history['learning_rates'], \n", "                       linewidth=2, color='green')\n", "        axes[1, 0].set_title('学习率变化')\n", "        axes[1, 0].set_xlabel('Epoch')\n", "        axes[1, 0].set_ylabel('Learning Rate')\n", "        axes[1, 0].grid(True, alpha=0.3)\n", "        \n", "        # 损失对比（对数尺度）\n", "        axes[1, 1].semilogy(epochs, self.training_history['train_loss'], \n", "                           label='Train Loss', linewidth=2, color='blue')\n", "        axes[1, 1].semilogy(epochs, self.training_history['val_loss'], \n", "                           label='Val Loss', linewidth=2, color='red')\n", "        axes[1, 1].set_title('损失曲线（对数尺度）')\n", "        axes[1, 1].set_xlabel('Epoch')\n", "        axes[1, 1].set_ylabel('Loss (log scale)')\n", "        axes[1, 1].legend()\n", "        axes[1, 1].grid(True, alpha=0.3)\n", "        \n", "        plt.tight_layout()\n", "        plt.show()\n", "        \n", "        # 打印训练总结\n", "        print(\"\\n训练总结:\")\n", "        print(f\"最佳验证损失: {min(self.training_history['val_loss']):.4f}\")\n", "        print(f\"最佳验证准确率: {max(self.training_history['val_acc']):.4f}\")\n", "        print(f\"最终训练损失: {self.training_history['train_loss'][-1]:.4f}\")\n", "        print(f\"最终验证损失: {self.training_history['val_loss'][-1]:.4f}\")\n", "\n", "print(\"训练器类定义完成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 训练流程演示\n", "\n", "现在让我们创建一个简单的模型和数据来演示完整的训练流程："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 创建简单的演示模型\n", "class SimpleLanguageModel(nn.Module):\n", "    \"\"\"\n", "    简单的语言模型用于演示训练流程\n", "    \"\"\"\n", "    \n", "    def __init__(self, vocab_size, embed_dim, hidden_dim, num_layers=2):\n", "        super(SimpleLanguageModel, self).__init__()\n", "        self.embedding = nn.Embedding(vocab_size, embed_dim)\n", "        self.lstm = nn.LSTM(embed_dim, hidden_dim, num_layers, \n", "                           batch_first=True, dropout=0.1)\n", "        self.output_proj = nn.Linear(hidden_dim, vocab_size)\n", "        self.dropout = nn.Dropout(0.1)\n", "    \n", "    def forward(self, x):\n", "        # x: (batch_size, seq_len)\n", "        embedded = self.embedding(x)  # (batch_size, seq_len, embed_dim)\n", "        embedded = self.dropout(embedded)\n", "        \n", "        lstm_out, _ = self.lstm(embedded)  # (batch_size, seq_len, hidden_dim)\n", "        output = self.output_proj(lstm_out)  # (batch_size, seq_len, vocab_size)\n", "        \n", "        return output\n", "\n", "# 创建模拟数据集\n", "class MockDataset(torch.utils.data.Dataset):\n", "    \"\"\"\n", "    模拟数据集用于演示\n", "    \"\"\"\n", "    \n", "    def __init__(self, num_samples, seq_len, vocab_size):\n", "        self.num_samples = num_samples\n", "        self.seq_len = seq_len\n", "        self.vocab_size = vocab_size\n", "        \n", "        # 生成随机序列数据\n", "        self.data = torch.randint(1, vocab_size, (num_samples, seq_len))\n", "        # 目标是输入序列向右移动一位\n", "        self.targets = torch.cat([\n", "            self.data[:, 1:], \n", "            torch.zeros(num_samples, 1, dtype=torch.long)\n", "        ], dim=1)\n", "    \n", "    def __len__(self):\n", "        return self.num_samples\n", "    \n", "    def __getitem__(self, idx):\n", "        return self.data[idx], self.targets[idx]\n", "\n", "def create_demo_data_loaders(config):\n", "    \"\"\"\n", "    创建演示用的数据加载器\n", "    \"\"\"\n", "    # 创建数据集\n", "    train_dataset = MockDataset(\n", "        num_samples=1000, \n", "        seq_len=config.max_seq_length, \n", "        vocab_size=config.vocab_size\n", "    )\n", "    \n", "    val_dataset = MockDataset(\n", "        num_samples=200, \n", "        seq_len=config.max_seq_length, \n", "        vocab_size=config.vocab_size\n", "    )\n", "    \n", "    # 创建数据加载器\n", "    train_loader = DataLoader(\n", "        train_dataset,\n", "        batch_size=config.batch_size,\n", "        shuffle=True,\n", "        num_workers=0,  # 演示时使用0避免多进程问题\n", "        pin_memory=False\n", "    )\n", "    \n", "    val_loader = DataLoader(\n", "        val_dataset,\n", "        batch_size=config.batch_size,\n", "        shuffle=False,\n", "        num_workers=0,\n", "        pin_memory=False\n", "    )\n", "    \n", "    return train_loader, val_loader\n", "\n", "def run_training_demo():\n", "    \"\"\"\n", "    运行训练演示\n", "    \"\"\"\n", "    print(\"=== 训练流程演示 ===\")\n", "    \n", "    # 创建配置\n", "    demo_config = TrainingConfig({\n", "        'vocab_size': 100,\n", "        'd_model': 128,\n", "        'num_heads': 4,\n", "        'num_layers': 2,\n", "        'max_seq_length': 20,\n", "        'batch_size': 8,\n", "        'num_epochs': 5,  # 少量epoch用于演示\n", "        'learning_rate': 1e-3,\n", "        'save_every': 2,\n", "        'log_every': 10,\n", "        'early_stopping': False  # 演示时关闭早停\n", "    })\n", "    \n", "    print(\"配置信息:\")\n", "    print(demo_config)\n", "    \n", "    # 创建模型\n", "    model = SimpleLanguageModel(\n", "        vocab_size=demo_config.vocab_size,\n", "        embed_dim=demo_config.d_model,\n", "        hidden_dim=demo_config.d_model\n", "    )\n", "    \n", "    print(f\"\\n模型信息:\")\n", "    print(f\"参数数量: {sum(p.numel() for p in model.parameters()):,}\")\n", "    \n", "    # 创建数据加载器\n", "    train_loader, val_loader = create_demo_data_loaders(demo_config)\n", "    \n", "    print(f\"\\n数据信息:\")\n", "    print(f\"训练批次数: {len(train_loader)}\")\n", "    print(f\"验证批次数: {len(val_loader)}\")\n", "    \n", "    # 创建训练器\n", "    trainer = Trainer(model, train_loader, val_loader, demo_config)\n", "    \n", "    # 开始训练\n", "    print(\"\\n开始训练...\")\n", "    history = trainer.train()\n", "    \n", "    # 可视化结果\n", "    trainer.plot_training_history()\n", "    \n", "    return trainer, history\n", "\n", "# 运行演示（注释掉以避免在导入时执行）\n", "# trainer, history = run_training_demo()\n", "\n", "print(\"\\n训练演示函数已定义\")\n", "print(\"运行 'trainer, history = run_training_demo()' 来开始演示\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 分布式训练基础\n", "\n", "对于大型模型，我们通常需要使用分布式训练来加速训练过程："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class DistributedTrainer(Trainer):\n", "    \"\"\"\n", "    分布式训练器\n", "    \n", "    扩展基础训练器以支持分布式训练\n", "    \"\"\"\n", "    \n", "    def __init__(self, model, train_loader, val_loader, config, local_rank=0):\n", "        self.local_rank = local_rank\n", "        self.world_size = torch.distributed.get_world_size() if torch.distributed.is_initialized() else 1\n", "        \n", "        # 设置设备\n", "        if torch.cuda.is_available():\n", "            torch.cuda.set_device(local_rank)\n", "            device = torch.device(f'cuda:{local_rank}')\n", "        else:\n", "            device = torch.device('cpu')\n", "        \n", "        # 包装模型为分布式模型\n", "        if torch.distributed.is_initialized() and torch.cuda.is_available():\n", "            model = nn.parallel.DistributedDataParallel(\n", "                model.to(device), \n", "                device_ids=[local_rank],\n", "                output_device=local_rank\n", "            )\n", "        \n", "        super().__init__(model, train_loader, val_loader, config)\n", "        self.device = device\n", "    \n", "    def _setup_logging(self):\n", "        \"\"\"只在主进程设置日志\"\"\"\n", "        if self.local_rank == 0:\n", "            super()._setup_logging()\n", "        else:\n", "            # 非主进程使用简单日志\n", "            self.logger = logging.getLogger(__name__)\n", "            self.writer = None\n", "            self.use_tensorboard = False\n", "    \n", "    def save_checkpoint(self, is_best=False, filename=None):\n", "        \"\"\"只在主进程保存检查点\"\"\"\n", "        if self.local_rank == 0:\n", "            # 如果是分布式模型，保存原始模型状态\n", "            if hasattr(self.model, 'module'):\n", "                model_state = self.model.module.state_dict()\n", "            else:\n", "                model_state = self.model.state_dict()\n", "            \n", "            if filename is None:\n", "                filename = f\"checkpoint_epoch_{self.current_epoch}.pth\"\n", "            \n", "            checkpoint = {\n", "                'epoch': self.current_epoch,\n", "                'model_state_dict': model_state,\n", "                'optimizer_state_dict': self.optimizer.state_dict(),\n", "                'scheduler_state_dict': self.scheduler.state_dict() if self.scheduler else None,\n", "                'best_val_loss': self.best_val_loss,\n", "                'training_history': self.training_history,\n", "                'config': self.config.default_config\n", "            }\n", "            \n", "            checkpoint_path = self.save_dir / filename\n", "            torch.save(checkpoint, checkpoint_path)\n", "            self.logger.info(f\"检查点已保存: {checkpoint_path}\")\n", "            \n", "            if is_best:\n", "                best_path = self.save_dir / \"best_model.pth\"\n", "                torch.save(checkpoint, best_path)\n", "                self.logger.info(f\"最佳模型已保存: {best_path}\")\n", "\n", "def setup_distributed_training():\n", "    \"\"\"\n", "    设置分布式训练环境\n", "    \n", "    Returns:\n", "        tuple: (local_rank, world_size)\n", "    \"\"\"\n", "    if 'RANK' in os.environ and 'WORLD_SIZE' in os.environ:\n", "        rank = int(os.environ['RANK'])\n", "        world_size = int(os.environ['WORLD_SIZE'])\n", "        local_rank = int(os.environ['LOCAL_RANK'])\n", "        \n", "        # 初始化分布式训练\n", "        torch.distributed.init_process_group(\n", "            backend='nccl' if torch.cuda.is_available() else 'gloo',\n", "            rank=rank,\n", "            world_size=world_size\n", "        )\n", "        \n", "        print(f\"分布式训练初始化: rank={rank}, world_size={world_size}, local_rank={local_rank}\")\n", "        return local_rank, world_size\n", "    else:\n", "        print(\"单GPU/CPU训练模式\")\n", "        return 0, 1\n", "\n", "def cleanup_distributed_training():\n", "    \"\"\"\n", "    清理分布式训练环境\n", "    \"\"\"\n", "    if torch.distributed.is_initialized():\n", "        torch.distributed.destroy_process_group()\n", "\n", "print(\"分布式训练类已定义\")\n", "print(\"\\n分布式训练使用示例:\")\n", "print(\"# 在命令行中运行:\")\n", "print(\"# torchrun --nproc_per_node=2 train_script.py\")\n", "print(\"\\n# 在脚本中:\")\n", "print(\"# local_rank, world_size = setup_distributed_training()\")\n", "print(\"# trainer = DistributedTrainer(model, train_loader, val_loader, config, local_rank)\")\n", "print(\"# trainer.train()\")\n", "print(\"# cleanup_distributed_training()\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 实际应用：R2Gen训练流程\n", "\n", "让我们看看如何将这个训练框架应用到实际的R2Gen模型训练中："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_r2gen_training_config():\n", "    \"\"\"\n", "    创建R2Gen模型的训练配置\n", "    \"\"\"\n", "    return TrainingConfig({\n", "        # 模型参数（基于R2Gen论文）\n", "        'model_name': 'r2gen',\n", "        'vocab_size': 1000,  # 根据实际词汇表大小调整\n", "        'd_model': 512,\n", "        'num_heads': 8,\n", "        'num_layers': 3,\n", "        'd_ff': 2048,\n", "        'max_seq_length': 60,\n", "        'dropout': 0.1,\n", "        \n", "        # 训练参数\n", "        'batch_size': 16,\n", "        'num_epochs': 100,\n", "        'learning_rate': 5e-5,  # 较小的学习率\n", "        'weight_decay': 1e-5,\n", "        'warmup_epochs': 5,\n", "        'grad_clip_norm': 1.0,\n", "        \n", "        # 优化器设置\n", "        'optimizer': 'adamw',\n", "        'scheduler': 'cosine',\n", "        'label_smoothing': 0.1,\n", "        \n", "        # 早停设置\n", "        'early_stopping': True,\n", "        'patience': 10,\n", "        'min_delta': 1e-4,\n", "        \n", "        # 保存设置\n", "        'save_dir': './checkpoints/r2gen',\n", "        'log_dir': './logs/r2gen',\n", "        'save_every': 5,\n", "        'eval_every': 1\n", "    })\n", "\n", "def train_r2gen_model():\n", "    \"\"\"\n", "    R2Gen模型训练的完整流程示例\n", "    \"\"\"\n", "    print(\"=== R2Gen模型训练流程 ===\")\n", "    \n", "    # 1. 创建配置\n", "    config = create_r2gen_training_config()\n", "    print(\"训练配置已创建\")\n", "    \n", "    # 2. 加载数据（这里使用模拟数据）\n", "    print(\"\\n加载数据...\")\n", "    # 在实际应用中，这里应该加载真实的医学影像和报告数据\n", "    # train_loader, val_loader = load_iu_xray_data(config)\n", "    train_loader, val_loader = create_demo_data_loaders(config)\n", "    print(f\"训练数据: {len(train_loader)} 批次\")\n", "    print(f\"验证数据: {len(val_loader)} 批次\")\n", "    \n", "    # 3. 创建模型（这里使用简化模型）\n", "    print(\"\\n创建模型...\")\n", "    # 在实际应用中，这里应该创建完整的R2Gen模型\n", "    # model = R2GenModel(config)\n", "    model = SimpleLanguageModel(\n", "        vocab_size=config.vocab_size,\n", "        embed_dim=config.d_model,\n", "        hidden_dim=config.d_model\n", "    )\n", "    print(f\"模型参数数量: {sum(p.numel() for p in model.parameters()):,}\")\n", "    \n", "    # 4. 创建训练器\n", "    print(\"\\n初始化训练器...\")\n", "    trainer = Trainer(model, train_loader, val_loader, config)\n", "    \n", "    # 5. 保存配置\n", "    config_path = Path(config.save_dir) / \"config.json\"\n", "    config_path.parent.mkdir(parents=True, exist_ok=True)\n", "    config.save(config_path)\n", "    print(f\"配置已保存到: {config_path}\")\n", "    \n", "    # 6. 开始训练\n", "    print(\"\\n开始训练...\")\n", "    try:\n", "        history = trainer.train()\n", "        print(\"训练完成！\")\n", "        \n", "        # 7. 可视化结果\n", "        trainer.plot_training_history()\n", "        \n", "        return trainer, history\n", "        \n", "    except Exception as e:\n", "        print(f\"训练过程中出现错误: {e}\")\n", "        return None, None\n", "\n", "print(\"R2Gen训练流程函数已定义\")\n", "print(\"\\n使用方法:\")\n", "print(\"trainer, history = train_r2gen_model()\")\n", "\n", "# 训练最佳实践总结\n", "print(\"\\n=== 训练最佳实践 ===\")\n", "print(\"1. 数据预处理:\")\n", "print(\"   - 确保数据质量和一致性\")\n", "print(\"   - 适当的数据增强\")\n", "print(\"   - 正确处理填充和掩码\")\n", "print(\"\\n2. 模型配置:\")\n", "print(\"   - 从较小的模型开始\")\n", "print(\"   - 逐步增加复杂度\")\n", "print(\"   - 使用预训练权重\")\n", "print(\"\\n3. 训练策略:\")\n", "print(\"   - 使用学习率预热\")\n", "print(\"   - 梯度裁剪防止爆炸\")\n", "print(\"   - 早停防止过拟合\")\n", "print(\"\\n4. 监控和调试:\")\n", "print(\"   - 实时监控损失和指标\")\n", "print(\"   - 定期保存检查点\")\n", "print(\"   - 记录详细日志\")\n", "print(\"\\n5. 资源管理:\")\n", "print(\"   - 合理设置批次大小\")\n", "print(\"   - 使用混合精度训练\")\n", "print(\"   - 分布式训练加速\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 小结\n", "\n", "在本节中，我们实现了一个完整的深度学习训练流程：\n", "\n", "### 核心组件\n", "\n", "1. **配置管理**：\n", "   - 统一管理所有训练参数\n", "   - 支持配置保存和加载\n", "   - 便于实验管理和复现\n", "\n", "2. **训练器类**：\n", "   - 封装完整的训练逻辑\n", "   - 支持训练、验证、保存、加载\n", "   - 集成监控和日志功能\n", "\n", "3. **检查点机制**：\n", "   - 定期保存模型状态\n", "   - 支持训练中断恢复\n", "   - 自动保存最佳模型\n", "\n", "4. **早停机制**：\n", "   - 防止过拟合\n", "   - 自动停止无效训练\n", "   - 节省计算资源\n", "\n", "### 关键特性\n", "\n", "1. **可扩展性**：\n", "   - 支持不同模型架构\n", "   - 支持分布式训练\n", "   - 易于添加新功能\n", "\n", "2. **鲁棒性**：\n", "   - 异常处理和恢复\n", "   - 梯度裁剪和稳定性\n", "   - 内存管理优化\n", "\n", "3. **可观测性**：\n", "   - 详细的日志记录\n", "   - TensorBoard集成\n", "   - 训练过程可视化\n", "\n", "4. **可重现性**：\n", "   - 随机种子设置\n", "   - 配置完整保存\n", "   - 确定性训练\n", "\n", "### 实际应用\n", "\n", "在R2Gen等医学报告生成项目中：\n", "- 使用完整的训练流程确保稳定性\n", "- 通过检查点机制应对长时间训练\n", "- 利用早停机制优化训练效率\n", "- 分布式训练处理大规模数据\n", "\n", "### 下一步\n", "\n", "在下一个notebook中，我们将学习评估指标和测试方法，了解如何全面评估模型的性能。\n", "\n", "### 关键收获\n", "\n", "- 完整的训练流程需要考虑多个方面\n", "- 良好的工程实践提高训练效率和稳定性\n", "- 监控和日志对调试和优化至关重要\n", "- 检查点和早停机制是必要的安全措施\n", "- 分布式训练是处理大规模任务的关键技术"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}