{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 09 - 评估指标与测试\n", "\n", "在这个notebook中，我们将深入学习如何评估医学报告生成模型的性能，包括各种评估指标的原理、实现和应用。\n", "\n", "## 学习目标\n", "\n", "- 理解文本生成评估的挑战\n", "- 掌握BLEU、ROUGE、METEOR等指标的原理\n", "- 实现各种评估指标的计算\n", "- 学习医学报告特有的评估方法\n", "- 设计完整的测试流程\n", "- 分析和解释评估结果\n", "- 进行错误分析和模型改进\n", "\n", "## 文本生成评估的挑战\n", "\n", "评估生成文本的质量是一个复杂的问题，因为：\n", "\n", "1. **多样性**：同一个输入可能有多个正确的输出\n", "2. **主观性**：文本质量评估往往带有主观因素\n", "3. **语义等价**：不同表达可能具有相同含义\n", "4. **领域特异性**：不同领域有不同的评估标准\n", "\n", "### 医学报告生成的特殊挑战\n", "\n", "医学报告生成还面临额外的挑战：\n", "- **临床准确性**：错误的医学信息可能导致严重后果\n", "- **完整性**：必须包含所有相关的医学发现\n", "- **专业术语**：需要正确使用医学术语\n", "- **结构化要求**：医学报告通常有特定的结构\n", "- **数据稀缺**：高质量的医学数据相对稀缺"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 导入必要的库\n", "import os\n", "import sys\n", "import json\n", "import re\n", "import string\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from collections import Counter\n", "from tqdm import tqdm\n", "import torch\n", "import nltk\n", "from nltk.translate.bleu_score import sentence_bleu, corpus_bleu, SmoothingFunction\n", "from nltk.translate.meteor_score import meteor_score\n", "from nltk.translate.nist_score import sentence_nist\n", "from nltk.translate.chrf_score import sentence_chrf\n", "from nltk.tokenize import word_tokenize\n", "from rouge import Rouge\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# 设置项目路径\n", "project_root = os.path.abspath('..')\n", "sys.path.append(project_root)\n", "\n", "print(f\"项目根目录: {project_root}\")\n", "\n", "# 下载NLTK资源（如果需要）\n", "try:\n", "    nltk.data.find('tokenizers/punkt')\n", "except LookupError:\n", "    nltk.download('punkt')\n", "    print(\"已下载NLTK punkt分词器\")\n", "\n", "try:\n", "    nltk.data.find('corpora/wordnet')\n", "except LookupError:\n", "    nltk.download('wordnet')\n", "    print(\"已下载NLTK wordnet语料库\")\n", "\n", "# 设置随机种子\n", "np.random.seed(42)\n", "torch.manual_seed(42)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 文本预处理\n", "\n", "在计算评估指标之前，我们需要对文本进行预处理："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class TextPreprocessor:\n", "    \"\"\"\n", "    文本预处理类\n", "    \n", "    用于标准化和清洗文本，以便进行评估\n", "    \"\"\"\n", "    \n", "    @staticmethod\n", "    def normalize_text(text):\n", "        \"\"\"\n", "        标准化文本\n", "        \n", "        Args:\n", "            text (str): 输入文本\n", "        \n", "        Returns:\n", "            str: 标准化后的文本\n", "        \"\"\"\n", "        # 转换为小写\n", "        text = text.lower()\n", "        \n", "        # 移除标点符号\n", "        translator = str.maketrans('', '', string.punctuation)\n", "        text = text.translate(translator)\n", "        \n", "        # 移除多余空格\n", "        text = re.sub(r'\\s+', ' ', text).strip()\n", "        \n", "        return text\n", "    \n", "    @staticmethod\n", "    def tokenize(text):\n", "        \"\"\"\n", "        分词\n", "        \n", "        Args:\n", "            text (str): 输入文本\n", "        \n", "        Returns:\n", "            list: 分词后的标记列表\n", "        \"\"\"\n", "        return word_tokenize(text)\n", "    \n", "    @staticmethod\n", "    def preprocess_for_evaluation(text, normalize=True, tokenize=True):\n", "        \"\"\"\n", "        评估前的文本预处理\n", "        \n", "        Args:\n", "            text (str): 输入文本\n", "            normalize (bool): 是否标准化\n", "            tokenize (bool): 是否分词\n", "        \n", "        Returns:\n", "            list or str: 预处理后的文本\n", "        \"\"\"\n", "        if normalize:\n", "            text = TextPreprocessor.normalize_text(text)\n", "        \n", "        if tokenize:\n", "            return TextPreprocessor.tokenize(text)\n", "        \n", "        return text\n", "    \n", "    @staticmethod\n", "    def preprocess_batch(texts, normalize=True, tokenize=True):\n", "        \"\"\"\n", "        批量预处理文本\n", "        \n", "        Args:\n", "            texts (list): 文本列表\n", "            normalize (bool): 是否标准化\n", "            tokenize (bool): 是否分词\n", "        \n", "        Returns:\n", "            list: 预处理后的文本列表\n", "        \"\"\"\n", "        return [TextPreprocessor.preprocess_for_evaluation(text, normalize, tokenize) \n", "                for text in texts]\n", "\n", "# 测试预处理函数\n", "example_text = \"The patient's chest X-ray shows clear lungs with no evidence of pneumonia. Heart size is normal.\"\n", "normalized_text = TextPreprocessor.normalize_text(example_text)\n", "tokenized_text = TextPreprocessor.tokenize(normalized_text)\n", "\n", "print(f\"原始文本: {example_text}\")\n", "print(f\"标准化文本: {normalized_text}\")\n", "print(f\"分词结果: {tokenized_text}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## BLEU指标详解\n", "\n", "BLEU (Bilingual Evaluation Understudy) 是最常用的文本生成评估指标之一。它基于n-gram精确度计算，衡量生成文本与参考文本的相似度。\n", "\n", "### BLEU计算原理\n", "\n", "BLEU分数的计算包括：\n", "1. **n-gram精确度**：计算1-gram到4-gram的精确度\n", "2. **简洁性惩罚**：惩罚过短的生成文本\n", "3. **几何平均**：对不同n-gram精确度取几何平均\n", "\n", "公式：$BLEU = BP \\times \\exp(\\sum_{n=1}^{N} w_n \\log p_n)$\n", "\n", "其中：\n", "- $BP$：简洁性惩罚 (Brevity Penalty)\n", "- $p_n$：n-gram精确度\n", "- $w_n$：权重（通常为1/N）"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class BLEUEvaluator:\n", "    \"\"\"\n", "    BLEU评估器\n", "    \n", "    实现BLEU指标的详细计算和分析\n", "    \"\"\"\n", "    \n", "    def __init__(self, max_n=4, smoothing=True):\n", "        self.max_n = max_n\n", "        self.smoothing = smoothing\n", "        self.smoothing_function = SmoothingFunction().method1 if smoothing else None\n", "    \n", "    def calculate_ngram_precision(self, candidate, references, n):\n", "        \"\"\"\n", "        计算n-gram精确度\n", "        \n", "        Args:\n", "            candidate (list): 候选文本的token列表\n", "            references (list): 参考文本的token列表的列表\n", "            n (int): n-gram的n值\n", "        \n", "        Returns:\n", "            float: n-gram精确度\n", "        \"\"\"\n", "        # 生成候选文本的n-gram\n", "        candidate_ngrams = [tuple(candidate[i:i+n]) for i in range(len(candidate)-n+1)]\n", "        candidate_counts = Counter(candidate_ngrams)\n", "        \n", "        # 生成参考文本的n-gram\n", "        max_ref_counts = Counter()\n", "        for reference in references:\n", "            ref_ngrams = [tuple(reference[i:i+n]) for i in range(len(reference)-n+1)]\n", "            ref_counts = Counter(ref_ngrams)\n", "            for ngram in ref_counts:\n", "                max_ref_counts[ngram] = max(max_ref_counts[ngram], ref_counts[ngram])\n", "        \n", "        # 计算匹配的n-gram数量\n", "        clipped_counts = 0\n", "        total_counts = 0\n", "        \n", "        for ngram, count in candidate_counts.items():\n", "            clipped_counts += min(count, max_ref_counts[ngram])\n", "            total_counts += count\n", "        \n", "        if total_counts == 0:\n", "            return 0.0\n", "        \n", "        return clipped_counts / total_counts\n", "    \n", "    def calculate_brevity_penalty(self, candidate_length, reference_lengths):\n", "        \"\"\"\n", "        计算简洁性惩罚\n", "        \n", "        Args:\n", "            candidate_length (int): 候选文本长度\n", "            reference_lengths (list): 参考文本长度列表\n", "        \n", "        Returns:\n", "            float: 简洁性惩罚值\n", "        \"\"\"\n", "        # 找到最接近候选文本长度的参考文本长度\n", "        closest_ref_length = min(reference_lengths, \n", "                                key=lambda x: abs(x - candidate_length))\n", "        \n", "        if candidate_length >= closest_ref_length:\n", "            return 1.0\n", "        else:\n", "            return np.exp(1 - closest_ref_length / candidate_length)\n", "    \n", "    def sentence_bleu(self, candidate, references):\n", "        \"\"\"\n", "        计算单句BLEU分数\n", "        \n", "        Args:\n", "            candidate (list): 候选文本的token列表\n", "            references (list): 参考文本的token列表的列表\n", "        \n", "        Returns:\n", "            dict: 包含BLEU分数和详细信息的字典\n", "        \"\"\"\n", "        if not candidate or not references:\n", "            return {'bleu': 0.0, 'precisions': [0.0] * self.max_n, 'bp': 0.0}\n", "        \n", "        # 计算各阶n-gram精确度\n", "        precisions = []\n", "        for n in range(1, self.max_n + 1):\n", "            precision = self.calculate_ngram_precision(candidate, references, n)\n", "            precisions.append(precision)\n", "        \n", "        # 计算简洁性惩罚\n", "        candidate_length = len(candidate)\n", "        reference_lengths = [len(ref) for ref in references]\n", "        bp = self.calculate_brevity_penalty(candidate_length, reference_lengths)\n", "        \n", "        # 计算BLEU分数\n", "        if any(p == 0 for p in precisions):\n", "            bleu_score = 0.0\n", "        else:\n", "            log_precisions = [np.log(p) for p in precisions]\n", "            bleu_score = bp * np.exp(np.mean(log_precisions))\n", "        \n", "        return {\n", "            'bleu': bleu_score,\n", "            'precisions': precisions,\n", "            'bp': bp,\n", "            'candidate_length': candidate_length,\n", "            'reference_lengths': reference_lengths\n", "        }\n", "    \n", "    def corpus_bleu(self, candidates, references_list):\n", "        \"\"\"\n", "        计算语料库级别的BLEU分数\n", "        \n", "        Args:\n", "            candidates (list): 候选文本列表\n", "            references_list (list): 参考文本列表的列表\n", "        \n", "        Returns:\n", "            dict: 包含语料库BLEU分数和统计信息的字典\n", "        \"\"\"\n", "        total_candidate_length = 0\n", "        total_reference_length = 0\n", "        total_clipped_counts = [0] * self.max_n\n", "        total_counts = [0] * self.max_n\n", "        \n", "        for candidate, references in zip(candidates, references_list):\n", "            total_candidate_length += len(candidate)\n", "            \n", "            # 找到最接近的参考长度\n", "            reference_lengths = [len(ref) for ref in references]\n", "            closest_ref_length = min(reference_lengths, \n", "                                   key=lambda x: abs(x - len(candidate)))\n", "            total_reference_length += closest_ref_length\n", "            \n", "            # 累计各阶n-gram统计\n", "            for n in range(1, self.max_n + 1):\n", "                candidate_ngrams = [tuple(candidate[i:i+n]) \n", "                                  for i in range(len(candidate)-n+1)]\n", "                candidate_counts = Counter(candidate_ngrams)\n", "                \n", "                max_ref_counts = Counter()\n", "                for reference in references:\n", "                    ref_ngrams = [tuple(reference[i:i+n]) \n", "                                for i in range(len(reference)-n+1)]\n", "                    ref_counts = Counter(ref_ngrams)\n", "                    for ngram in ref_counts:\n", "                        max_ref_counts[ngram] = max(max_ref_counts[ngram], \n", "                                                  ref_counts[ngram])\n", "                \n", "                clipped_counts = sum(min(count, max_ref_counts[ngram]) \n", "                                   for ngram, count in candidate_counts.items())\n", "                total_clipped_counts[n-1] += clipped_counts\n", "                total_counts[n-1] += sum(candidate_counts.values())\n", "        \n", "        # 计算精确度\n", "        precisions = [clipped / total if total > 0 else 0.0 \n", "                     for clipped, total in zip(total_clipped_counts, total_counts)]\n", "        \n", "        # 计算简洁性惩罚\n", "        if total_candidate_length >= total_reference_length:\n", "            bp = 1.0\n", "        else:\n", "            bp = np.exp(1 - total_reference_length / total_candidate_length)\n", "        \n", "        # 计算BLEU分数\n", "        if any(p == 0 for p in precisions):\n", "            bleu_score = 0.0\n", "        else:\n", "            log_precisions = [np.log(p) for p in precisions]\n", "            bleu_score = bp * np.exp(np.mean(log_precisions))\n", "        \n", "        return {\n", "            'bleu': bleu_score,\n", "            'precisions': precisions,\n", "            'bp': bp,\n", "            'total_candidate_length': total_candidate_length,\n", "            'total_reference_length': total_reference_length\n", "        }\n", "\n", "# 演示BLEU计算\n", "def demonstrate_bleu():\n", "    print(\"=== BLEU指标演示 ===\")\n", "    \n", "    # 示例数据\n", "    candidate = \"the patient has clear lungs\".split()\n", "    references = [\n", "        \"the patient has clear lungs\".split(),\n", "        \"patient shows clear lung fields\".split(),\n", "        \"lungs are clear in this patient\".split()\n", "    ]\n", "    \n", "    print(f\"候选文本: {' '.join(candidate)}\")\n", "    print(f\"参考文本:\")\n", "    for i, ref in enumerate(references):\n", "        print(f\"  {i+1}: {' '.join(ref)}\")\n", "    \n", "    # 计算BLEU\n", "    evaluator = BLEUEvaluator()\n", "    result = evaluator.sentence_bleu(candidate, references)\n", "    \n", "    print(f\"\\nBLEU分数: {result['bleu']:.4f}\")\n", "    print(f\"各阶n-gram精确度:\")\n", "    for i, precision in enumerate(result['precisions']):\n", "        print(f\"  {i+1}-gram: {precision:.4f}\")\n", "    print(f\"简洁性惩罚: {result['bp']:.4f}\")\n", "    \n", "    return result\n", "\n", "bleu_result = demonstrate_bleu()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## ROUGE指标详解\n", "\n", "ROUGE (Recall-Oriented Understudy for Gisting Evaluation) 是另一个重要的文本评估指标，特别适用于摘要任务。与BLEU关注精确度不同，ROUGE更关注召回率。\n", "\n", "### ROUGE变体\n", "\n", "1. **ROUGE-N**：基于n-gram的召回率\n", "2. **ROUGE-L**：基于最长公共子序列(LCS)\n", "3. **ROUGE-W**：加权最长公共子序列\n", "4. **ROUGE-S**：基于跳跃二元组(Skip-bigram)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class ROUGEEvaluator:\n", "    \"\"\"\n", "    ROUGE评估器\n", "    \n", "    实现ROUGE指标的详细计算\n", "    \"\"\"\n", "    \n", "    def __init__(self):\n", "        try:\n", "            self.rouge = Rouge()\n", "        except:\n", "            print(\"警告: Rouge库未安装，将使用简化实现\")\n", "            self.rouge = None\n", "    \n", "    def calculate_rouge_n(self, candidate, reference, n=1):\n", "        \"\"\"\n", "        计算ROUGE-N分数\n", "        \n", "        Args:\n", "            candidate (list): 候选文本token列表\n", "            reference (list): 参考文本token列表\n", "            n (int): n-gram的n值\n", "        \n", "        Returns:\n", "            dict: 包含精确度、召回率和F1分数的字典\n", "        \"\"\"\n", "        # 生成n-gram\n", "        candidate_ngrams = set(tuple(candidate[i:i+n]) \n", "                              for i in range(len(candidate)-n+1))\n", "        reference_ngrams = set(tuple(reference[i:i+n]) \n", "                              for i in range(len(reference)-n+1))\n", "        \n", "        # 计算交集\n", "        intersection = candidate_ngrams & reference_ngrams\n", "        \n", "        # 计算精确度、召回率和F1\n", "        precision = len(intersection) / len(candidate_ngrams) if candidate_ngrams else 0.0\n", "        recall = len(intersection) / len(reference_ngrams) if reference_ngrams else 0.0\n", "        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0.0\n", "        \n", "        return {\n", "            'precision': precision,\n", "            'recall': recall,\n", "            'f1': f1\n", "        }\n", "    \n", "    def calculate_lcs_length(self, seq1, seq2):\n", "        \"\"\"\n", "        计算最长公共子序列长度\n", "        \n", "        Args:\n", "            seq1 (list): 序列1\n", "            seq2 (list): 序列2\n", "        \n", "        Returns:\n", "            int: 最长公共子序列长度\n", "        \"\"\"\n", "        m, n = len(seq1), len(seq2)\n", "        dp = [[0] * (n + 1) for _ in range(m + 1)]\n", "        \n", "        for i in range(1, m + 1):\n", "            for j in range(1, n + 1):\n", "                if seq1[i-1] == seq2[j-1]:\n", "                    dp[i][j] = dp[i-1][j-1] + 1\n", "                else:\n", "                    dp[i][j] = max(dp[i-1][j], dp[i][j-1])\n", "        \n", "        return dp[m][n]\n", "    \n", "    def calculate_rouge_l(self, candidate, reference):\n", "        \"\"\"\n", "        计算ROUGE-L分数\n", "        \n", "        Args:\n", "            candidate (list): 候选文本token列表\n", "            reference (list): 参考文本token列表\n", "        \n", "        Returns:\n", "            dict: 包含精确度、召回率和F1分数的字典\n", "        \"\"\"\n", "        lcs_length = self.calculate_lcs_length(candidate, reference)\n", "        \n", "        precision = lcs_length / len(candidate) if candidate else 0.0\n", "        recall = lcs_length / len(reference) if reference else 0.0\n", "        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0.0\n", "        \n", "        return {\n", "            'precision': precision,\n", "            'recall': recall,\n", "            'f1': f1,\n", "            'lcs_length': lcs_length\n", "        }\n", "    \n", "    def evaluate(self, candidate, reference):\n", "        \"\"\"\n", "        计算所有ROUGE指标\n", "        \n", "        Args:\n", "            candidate (str or list): 候选文本\n", "            reference (str or list): 参考文本\n", "        \n", "        Returns:\n", "            dict: 包含所有ROUGE指标的字典\n", "        \"\"\"\n", "        # 预处理\n", "        if isinstance(candidate, str):\n", "            candidate = TextPreprocessor.preprocess_for_evaluation(candidate)\n", "        if isinstance(reference, str):\n", "            reference = TextPreprocessor.preprocess_for_evaluation(reference)\n", "        \n", "        results = {}\n", "        \n", "        # ROUGE-1\n", "        results['rouge-1'] = self.calculate_rouge_n(candidate, reference, 1)\n", "        \n", "        # ROUGE-2\n", "        results['rouge-2'] = self.calculate_rouge_n(candidate, reference, 2)\n", "        \n", "        # ROUGE-L\n", "        results['rouge-l'] = self.calculate_rouge_l(candidate, reference)\n", "        \n", "        return results\n", "\n", "class METEOREvaluator:\n", "    \"\"\"\n", "    METEOR评估器\n", "    \n", "    METEOR考虑了同义词和词干，比BLEU更加灵活\n", "    \"\"\"\n", "    \n", "    def __init__(self):\n", "        pass\n", "    \n", "    def calculate_meteor(self, candidate, reference):\n", "        \"\"\"\n", "        计算METEOR分数\n", "        \n", "        Args:\n", "            candidate (str or list): 候选文本\n", "            reference (str or list): 参考文本\n", "        \n", "        Returns:\n", "            float: METEOR分数\n", "        \"\"\"\n", "        try:\n", "            # 如果是token列表，转换为字符串\n", "            if isinstance(candidate, list):\n", "                candidate = ' '.join(candidate)\n", "            if isinstance(reference, list):\n", "                reference = ' '.join(reference)\n", "            \n", "            # 使用NLTK的METEOR实现\n", "            score = meteor_score([reference], candidate)\n", "            return score\n", "        except Exception as e:\n", "            print(f\"METEOR计算错误: {e}\")\n", "            return 0.0\n", "\n", "# 演示ROUGE和METEOR\n", "def demonstrate_rouge_meteor():\n", "    print(\"\\n=== ROUGE和METEOR指标演示 ===\")\n", "    \n", "    candidate = \"the patient has clear lungs with no abnormalities\"\n", "    reference = \"patient shows clear lung fields without any abnormal findings\"\n", "    \n", "    print(f\"候选文本: {candidate}\")\n", "    print(f\"参考文本: {reference}\")\n", "    \n", "    # ROUGE评估\n", "    rouge_evaluator = ROUGEEvaluator()\n", "    rouge_scores = rouge_evaluator.evaluate(candidate, reference)\n", "    \n", "    print(f\"\\nROUGE分数:\")\n", "    for metric, scores in rouge_scores.items():\n", "        print(f\"  {metric.upper()}:\")\n", "        print(f\"    精确度: {scores['precision']:.4f}\")\n", "        print(f\"    召回率: {scores['recall']:.4f}\")\n", "        print(f\"    F1分数: {scores['f1']:.4f}\")\n", "    \n", "    # METEOR评估\n", "    meteor_evaluator = METEOREvaluator()\n", "    meteor_score = meteor_evaluator.calculate_meteor(candidate, reference)\n", "    \n", "    print(f\"\\nMETEOR分数: {meteor_score:.4f}\")\n", "    \n", "    return rouge_scores, meteor_score\n", "\n", "rouge_scores, meteor_score = demonstrate_rouge_meteor()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 医学报告特有的评估指标\n", "\n", "除了通用的文本生成指标，医学报告生成还需要考虑领域特定的评估方法："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class MedicalReportEvaluator:\n", "    \"\"\"\n", "    医学报告专用评估器\n", "    \n", "    实现医学报告生成的特殊评估指标\n", "    \"\"\"\n", "    \n", "    def __init__(self):\n", "        # 医学术语词典（简化版）\n", "        self.medical_terms = {\n", "            'anatomy': ['heart', 'lung', 'liver', 'kidney', 'spine', 'chest', 'abdomen'],\n", "            'findings': ['normal', 'abnormal', 'clear', 'opacity', 'effusion', 'pneumonia', 'fracture'],\n", "            'severity': ['mild', 'moderate', 'severe', 'stable', 'improved', 'worsened'],\n", "            'location': ['left', 'right', 'bilateral', 'upper', 'lower', 'anterior', 'posterior']\n", "        }\n", "        \n", "        # 关键医学概念\n", "        self.critical_concepts = [\n", "            'pneumonia', 'fracture', 'tumor', 'mass', 'effusion', \n", "            'pneumothorax', 'cardiomegaly', 'atelectasis'\n", "        ]\n", "    \n", "    def extract_medical_terms(self, text):\n", "        \"\"\"\n", "        提取文本中的医学术语\n", "        \n", "        Args:\n", "            text (str): 输入文本\n", "        \n", "        Returns:\n", "            dict: 按类别分组的医学术语\n", "        \"\"\"\n", "        text_lower = text.lower()\n", "        extracted = {category: [] for category in self.medical_terms}\n", "        \n", "        for category, terms in self.medical_terms.items():\n", "            for term in terms:\n", "                if term in text_lower:\n", "                    extracted[category].append(term)\n", "        \n", "        return extracted\n", "    \n", "    def calculate_medical_term_coverage(self, candidate, reference):\n", "        \"\"\"\n", "        计算医学术语覆盖率\n", "        \n", "        Args:\n", "            candidate (str): 候选文本\n", "            reference (str): 参考文本\n", "        \n", "        Returns:\n", "            dict: 医学术语覆盖率统计\n", "        \"\"\"\n", "        candidate_terms = self.extract_medical_terms(candidate)\n", "        reference_terms = self.extract_medical_terms(reference)\n", "        \n", "        coverage_stats = {}\n", "        \n", "        for category in self.medical_terms:\n", "            ref_set = set(reference_terms[category])\n", "            cand_set = set(candidate_terms[category])\n", "            \n", "            if ref_set:\n", "                coverage = len(cand_set & ref_set) / len(ref_set)\n", "                precision = len(cand_set & ref_set) / len(cand_set) if cand_set else 0.0\n", "            else:\n", "                coverage = 1.0 if not cand_set else 0.0\n", "                precision = 1.0 if not cand_set else 0.0\n", "            \n", "            coverage_stats[category] = {\n", "                'coverage': coverage,\n", "                'precision': precision,\n", "                'reference_terms': list(ref_set),\n", "                'candidate_terms': list(cand_set),\n", "                'matched_terms': list(cand_set & ref_set)\n", "            }\n", "        \n", "        return coverage_stats\n", "    \n", "    def calculate_critical_finding_accuracy(self, candidate, reference):\n", "        \"\"\"\n", "        计算关键发现的准确性\n", "        \n", "        Args:\n", "            candidate (str): 候选文本\n", "            reference (str): 参考文本\n", "        \n", "        Returns:\n", "            dict: 关键发现准确性统计\n", "        \"\"\"\n", "        candidate_lower = candidate.lower()\n", "        reference_lower = reference.lower()\n", "        \n", "        candidate_findings = [term for term in self.critical_concepts \n", "                            if term in candidate_lower]\n", "        reference_findings = [term for term in self.critical_concepts \n", "                            if term in reference_lower]\n", "        \n", "        candidate_set = set(candidate_findings)\n", "        reference_set = set(reference_findings)\n", "        \n", "        true_positives = len(candidate_set & reference_set)\n", "        false_positives = len(candidate_set - reference_set)\n", "        false_negatives = len(reference_set - candidate_set)\n", "        \n", "        precision = true_positives / (true_positives + false_positives) if (true_positives + false_positives) > 0 else 0.0\n", "        recall = true_positives / (true_positives + false_negatives) if (true_positives + false_negatives) > 0 else 0.0\n", "        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0.0\n", "        \n", "        return {\n", "            'precision': precision,\n", "            'recall': recall,\n", "            'f1': f1,\n", "            'true_positives': true_positives,\n", "            'false_positives': false_positives,\n", "            'false_negatives': false_negatives,\n", "            'candidate_findings': candidate_findings,\n", "            'reference_findings': reference_findings\n", "        }\n", "    \n", "    def calculate_report_structure_score(self, candidate, reference):\n", "        \"\"\"\n", "        计算报告结构相似度\n", "        \n", "        Args:\n", "            candidate (str): 候选文本\n", "            reference (str): 参考文本\n", "        \n", "        Returns:\n", "            dict: 结构相似度统计\n", "        \"\"\"\n", "        # 简化的结构分析：基于句子数量和长度分布\n", "        candidate_sentences = candidate.split('.')\n", "        reference_sentences = reference.split('.')\n", "        \n", "        candidate_sentences = [s.strip() for s in candidate_sentences if s.strip()]\n", "        reference_sentences = [s.strip() for s in reference_sentences if s.strip()]\n", "        \n", "        # 句子数量相似度\n", "        sent_count_similarity = 1 - abs(len(candidate_sentences) - len(reference_sentences)) / max(len(candidate_sentences), len(reference_sentences), 1)\n", "        \n", "        # 平均句子长度相似度\n", "        candidate_avg_len = np.mean([len(s.split()) for s in candidate_sentences]) if candidate_sentences else 0\n", "        reference_avg_len = np.mean([len(s.split()) for s in reference_sentences]) if reference_sentences else 0\n", "        \n", "        len_similarity = 1 - abs(candidate_avg_len - reference_avg_len) / max(candidate_avg_len, reference_avg_len, 1)\n", "        \n", "        # 综合结构分数\n", "        structure_score = (sent_count_similarity + len_similarity) / 2\n", "        \n", "        return {\n", "            'structure_score': structure_score,\n", "            'sentence_count_similarity': sent_count_similarity,\n", "            'length_similarity': len_similarity,\n", "            'candidate_sentence_count': len(candidate_sentences),\n", "            'reference_sentence_count': len(reference_sentences),\n", "            'candidate_avg_sentence_length': candidate_avg_len,\n", "            'reference_avg_sentence_length': reference_avg_len\n", "        }\n", "\n", "# 演示医学报告评估\n", "def demonstrate_medical_evaluation():\n", "    print(\"\\n=== 医学报告专用评估演示 ===\")\n", "    \n", "    candidate = \"The chest X-ray shows clear lungs with no evidence of pneumonia. Heart size is normal. No fractures are seen.\"\n", "    reference = \"Chest radiograph demonstrates clear lung fields without pneumonia. Cardiac silhouette is within normal limits. No acute fractures identified.\"\n", "    \n", "    print(f\"候选报告: {candidate}\")\n", "    print(f\"参考报告: {reference}\")\n", "    \n", "    evaluator = MedicalReportEvaluator()\n", "    \n", "    # 医学术语覆盖率\n", "    term_coverage = evaluator.calculate_medical_term_coverage(candidate, reference)\n", "    print(f\"\\n医学术语覆盖率:\")\n", "    for category, stats in term_coverage.items():\n", "        if stats['reference_terms'] or stats['candidate_terms']:\n", "            print(f\"  {category}: 覆盖率={stats['coverage']:.3f}, 精确度={stats['precision']:.3f}\")\n", "            print(f\"    参考术语: {stats['reference_terms']}\")\n", "            print(f\"    候选术语: {stats['candidate_terms']}\")\n", "            print(f\"    匹配术语: {stats['matched_terms']}\")\n", "    \n", "    # 关键发现准确性\n", "    critical_accuracy = evaluator.calculate_critical_finding_accuracy(candidate, reference)\n", "    print(f\"\\n关键发现准确性:\")\n", "    print(f\"  精确度: {critical_accuracy['precision']:.3f}\")\n", "    print(f\"  召回率: {critical_accuracy['recall']:.3f}\")\n", "    print(f\"  F1分数: {critical_accuracy['f1']:.3f}\")\n", "    print(f\"  参考发现: {critical_accuracy['reference_findings']}\")\n", "    print(f\"  候选发现: {critical_accuracy['candidate_findings']}\")\n", "    \n", "    # 报告结构相似度\n", "    structure_score = evaluator.calculate_report_structure_score(candidate, reference)\n", "    print(f\"\\n报告结构相似度:\")\n", "    print(f\"  综合结构分数: {structure_score['structure_score']:.3f}\")\n", "    print(f\"  句子数量相似度: {structure_score['sentence_count_similarity']:.3f}\")\n", "    print(f\"  句子长度相似度: {structure_score['length_similarity']:.3f}\")\n", "    \n", "    return term_coverage, critical_accuracy, structure_score\n", "\n", "medical_results = demonstrate_medical_evaluation()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 完整的评估框架\n", "\n", "现在我们将所有评估指标整合到一个完整的评估框架中："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class ComprehensiveEvaluator:\n", "    \"\"\"\n", "    综合评估器\n", "    \n", "    整合所有评估指标，提供完整的模型评估\n", "    \"\"\"\n", "    \n", "    def __init__(self):\n", "        self.bleu_evaluator = BLEUEvaluator()\n", "        self.rouge_evaluator = ROUGEEvaluator()\n", "        self.meteor_evaluator = METEOREvaluator()\n", "        self.medical_evaluator = MedicalReportEvaluator()\n", "    \n", "    def evaluate_single_pair(self, candidate, reference):\n", "        \"\"\"\n", "        评估单个候选-参考文本对\n", "        \n", "        Args:\n", "            candidate (str): 候选文本\n", "            reference (str): 参考文本\n", "        \n", "        Returns:\n", "            dict: 包含所有评估指标的字典\n", "        \"\"\"\n", "        results = {}\n", "        \n", "        # 预处理\n", "        candidate_tokens = TextPreprocessor.preprocess_for_evaluation(candidate)\n", "        reference_tokens = TextPreprocessor.preprocess_for_evaluation(reference)\n", "        \n", "        # BLEU评估\n", "        bleu_result = self.bleu_evaluator.sentence_bleu(candidate_tokens, [reference_tokens])\n", "        results['bleu'] = bleu_result['bleu']\n", "        results['bleu_precisions'] = bleu_result['precisions']\n", "        \n", "        # ROUGE评估\n", "        rouge_results = self.rouge_evaluator.evaluate(candidate, reference)\n", "        for metric, scores in rouge_results.items():\n", "            results[f'{metric}_f1'] = scores['f1']\n", "            results[f'{metric}_precision'] = scores['precision']\n", "            results[f'{metric}_recall'] = scores['recall']\n", "        \n", "        # METEOR评估\n", "        results['meteor'] = self.meteor_evaluator.calculate_meteor(candidate, reference)\n", "        \n", "        # 医学报告专用评估\n", "        term_coverage = self.medical_evaluator.calculate_medical_term_coverage(candidate, reference)\n", "        critical_accuracy = self.medical_evaluator.calculate_critical_finding_accuracy(candidate, reference)\n", "        structure_score = self.medical_evaluator.calculate_report_structure_score(candidate, reference)\n", "        \n", "        # 整合医学评估结果\n", "        results['medical_term_coverage'] = np.mean([stats['coverage'] for stats in term_coverage.values()])\n", "        results['critical_finding_f1'] = critical_accuracy['f1']\n", "        results['structure_score'] = structure_score['structure_score']\n", "        \n", "        return results\n", "    \n", "    def evaluate_corpus(self, candidates, references, verbose=True):\n", "        \"\"\"\n", "        评估整个语料库\n", "        \n", "        Args:\n", "            candidates (list): 候选文本列表\n", "            references (list): 参考文本列表\n", "            verbose (bool): 是否显示详细信息\n", "        \n", "        Returns:\n", "            dict: 包含平均评估指标的字典\n", "        \"\"\"\n", "        if len(candidates) != len(references):\n", "            raise ValueError(\"候选文本和参考文本数量不匹配\")\n", "        \n", "        all_results = []\n", "        \n", "        if verbose:\n", "            iterator = tqdm(zip(candidates, references), total=len(candidates), desc=\"评估进度\")\n", "        else:\n", "            iterator = zip(candidates, references)\n", "        \n", "        for candidate, reference in iterator:\n", "            result = self.evaluate_single_pair(candidate, reference)\n", "            all_results.append(result)\n", "        \n", "        # 计算平均值\n", "        avg_results = {}\n", "        for key in all_results[0].keys():\n", "            if isinstance(all_results[0][key], list):\n", "                # 对于列表类型（如BLEU precisions），计算每个位置的平均值\n", "                avg_results[key] = np.mean([result[key] for result in all_results], axis=0).tolist()\n", "            else:\n", "                avg_results[key] = np.mean([result[key] for result in all_results])\n", "        \n", "        # 添加统计信息\n", "        avg_results['num_samples'] = len(candidates)\n", "        \n", "        return avg_results, all_results\n", "    \n", "    def generate_evaluation_report(self, results, save_path=None):\n", "        \"\"\"\n", "        生成评估报告\n", "        \n", "        Args:\n", "            results (dict): 评估结果\n", "            save_path (str): 保存路径\n", "        \n", "        Returns:\n", "            str: 评估报告文本\n", "        \"\"\"\n", "        report = \"\\n\" + \"=\" * 60 + \"\\n\"\n", "        report += \"医学报告生成模型评估报告\\n\"\n", "        report += \"=\" * 60 + \"\\n\\n\"\n", "        \n", "        # 基础指标\n", "        report += \"基础文本生成指标:\\n\"\n", "        report += \"-\" * 30 + \"\\n\"\n", "        report += f\"BLEU分数:        {results['bleu']:.4f}\\n\"\n", "        report += f\"ROUGE-1 F1:      {results['rouge-1_f1']:.4f}\\n\"\n", "        report += f\"ROUGE-2 F1:      {results['rouge-2_f1']:.4f}\\n\"\n", "        report += f\"ROUGE-L F1:      {results['rouge-l_f1']:.4f}\\n\"\n", "        report += f\"METEOR分数:      {results['meteor']:.4f}\\n\\n\"\n", "        \n", "        # 医学专用指标\n", "        report += \"医学报告专用指标:\\n\"\n", "        report += \"-\" * 30 + \"\\n\"\n", "        report += f\"医学术语覆盖率:   {results['medical_term_coverage']:.4f}\\n\"\n", "        report += f\"关键发现F1:      {results['critical_finding_f1']:.4f}\\n\"\n", "        report += f\"报告结构相似度:   {results['structure_score']:.4f}\\n\\n\"\n", "        \n", "        # 详细的BLEU精确度\n", "        report += \"BLEU详细精确度:\\n\"\n", "        report += \"-\" * 30 + \"\\n\"\n", "        for i, precision in enumerate(results['bleu_precisions']):\n", "            report += f\"{i+1}-gram精确度:   {precision:.4f}\\n\"\n", "        \n", "        report += \"\\n\" + \"=\" * 60 + \"\\n\"\n", "        \n", "        if save_path:\n", "            with open(save_path, 'w', encoding='utf-8') as f:\n", "                f.write(report)\n", "            print(f\"评估报告已保存到: {save_path}\")\n", "        \n", "        return report\n", "\n", "# 演示完整评估流程\n", "def demonstrate_comprehensive_evaluation():\n", "    print(\"\\n=== 完整评估流程演示 ===\")\n", "    \n", "    # 模拟数据\n", "    candidates = [\n", "        \"The chest X-ray shows clear lungs with no evidence of pneumonia. Heart size is normal.\",\n", "        \"Bilateral lung fields are clear. No acute cardiopulmonary abnormalities.\",\n", "        \"Normal chest radiograph. No pneumonia or fractures identified.\"\n", "    ]\n", "    \n", "    references = [\n", "        \"Chest radiograph demonstrates clear lung fields without pneumonia. Cardiac silhouette is within normal limits.\",\n", "        \"Both lungs appear clear with no acute abnormalities. Heart size is normal.\",\n", "        \"Chest X-ray is normal. No evidence of pneumonia, fractures, or other acute findings.\"\n", "    ]\n", "    \n", "    print(f\"评估样本数量: {len(candidates)}\")\n", "    \n", "    # 创建评估器\n", "    evaluator = ComprehensiveEvaluator()\n", "    \n", "    # 进行评估\n", "    avg_results, individual_results = evaluator.evaluate_corpus(candidates, references)\n", "    \n", "    # 生成报告\n", "    report = evaluator.generate_evaluation_report(avg_results)\n", "    print(report)\n", "    \n", "    # 可视化结果\n", "    visualize_evaluation_results(avg_results)\n", "    \n", "    return avg_results, individual_results\n", "\n", "def visualize_evaluation_results(results):\n", "    \"\"\"\n", "    可视化评估结果\n", "    \n", "    Args:\n", "        results (dict): 评估结果\n", "    \"\"\"\n", "    # 准备数据\n", "    metrics = ['bleu', 'rouge-1_f1', 'rouge-2_f1', 'rouge-l_f1', 'meteor', \n", "              'medical_term_coverage', 'critical_finding_f1', 'structure_score']\n", "    values = [results[metric] for metric in metrics]\n", "    \n", "    # 创建图表\n", "    fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "    \n", "    # 1. 基础指标雷达图\n", "    basic_metrics = ['bleu', 'rouge-1_f1', 'rouge-2_f1', 'rouge-l_f1', 'meteor']\n", "    basic_values = [results[metric] for metric in basic_metrics]\n", "    \n", "    angles = np.linspace(0, 2 * np.pi, len(basic_metrics), endpoint=False).tolist()\n", "    basic_values += basic_values[:1]  # 闭合图形\n", "    angles += angles[:1]\n", "    \n", "    ax1 = plt.subplot(2, 2, 1, projection='polar')\n", "    ax1.plot(angles, basic_values, 'o-', linewidth=2, color='blue')\n", "    ax1.fill(angles, basic_values, alpha=0.25, color='blue')\n", "    ax1.set_xticks(angles[:-1])\n", "    ax1.set_xticklabels([m.upper() for m in basic_metrics])\n", "    ax1.set_ylim(0, 1)\n", "    ax1.set_title('基础文本生成指标', pad=20)\n", "    \n", "    # 2. 医学专用指标\n", "    medical_metrics = ['medical_term_coverage', 'critical_finding_f1', 'structure_score']\n", "    medical_values = [results[metric] for metric in medical_metrics]\n", "    \n", "    axes[0, 1].bar(range(len(medical_metrics)), medical_values, \n", "                   color=['red', 'green', 'orange'], alpha=0.7)\n", "    axes[0, 1].set_xticks(range(len(medical_metrics)))\n", "    axes[0, 1].set_xticklabels(['术语覆盖', '关键发现', '结构相似'], rotation=45)\n", "    axes[0, 1].set_ylabel('分数')\n", "    axes[0, 1].set_title('医学报告专用指标')\n", "    axes[0, 1].set_ylim(0, 1)\n", "    \n", "    # 添加数值标签\n", "    for i, v in enumerate(medical_values):\n", "        axes[0, 1].text(i, v + 0.02, f'{v:.3f}', ha='center', va='bottom')\n", "    \n", "    # 3. BLEU精确度分解\n", "    bleu_precisions = results['bleu_precisions']\n", "    axes[1, 0].plot(range(1, len(bleu_precisions) + 1), bleu_precisions, \n", "                   'o-', linewidth=2, markersize=8, color='purple')\n", "    axes[1, 0].set_xlabel('n-gram')\n", "    axes[1, 0].set_ylabel('精确度')\n", "    axes[1, 0].set_title('BLEU n-gram精确度分解')\n", "    axes[1, 0].grid(True, alpha=0.3)\n", "    axes[1, 0].set_xticks(range(1, len(bleu_precisions) + 1))\n", "    \n", "    # 4. 综合指标对比\n", "    all_metrics = ['BLEU', 'ROUGE-1', 'ROUGE-L', 'METEOR', '医学术语', '关键发现', '结构']\n", "    all_values = [results['bleu'], results['rouge-1_f1'], results['rouge-l_f1'], \n", "                 results['meteor'], results['medical_term_coverage'], \n", "                 results['critical_finding_f1'], results['structure_score']]\n", "    \n", "    colors = plt.cm.Set3(np.linspace(0, 1, len(all_metrics)))\n", "    bars = axes[1, 1].bar(range(len(all_metrics)), all_values, color=colors, alpha=0.8)\n", "    axes[1, 1].set_xticks(range(len(all_metrics)))\n", "    axes[1, 1].set_xticklabels(all_metrics, rotation=45)\n", "    axes[1, 1].set_ylabel('分数')\n", "    axes[1, 1].set_title('所有评估指标综合对比')\n", "    axes[1, 1].set_ylim(0, 1)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# 运行完整评估演示\n", "comprehensive_results = demonstrate_comprehensive_evaluation()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 小结\n", "\n", "在本节中，我们深入学习了医学报告生成模型的评估方法：\n", "\n", "### 核心评估指标\n", "\n", "1. **BLEU指标**：\n", "   - 基于n-gram精确度的评估\n", "   - 包含简洁性惩罚机制\n", "   - 适用于评估生成文本的流畅性\n", "\n", "2. **ROUGE指标**：\n", "   - 关注召回率的评估方法\n", "   - ROUGE-N、ROUGE-L等多种变体\n", "   - 特别适用于摘要任务\n", "\n", "3. **METEOR指标**：\n", "   - 考虑同义词和词干的灵活评估\n", "   - 比BLEU更加语义友好\n", "   - 与人类判断相关性更高\n", "\n", "### 医学报告特有评估\n", "\n", "1. **医学术语覆盖率**：\n", "   - 评估医学专业术语的使用\n", "   - 按解剖结构、发现、严重程度等分类\n", "   - 确保专业性和准确性\n", "\n", "2. **关键发现准确性**：\n", "   - 重点评估重要医学发现\n", "   - 防止遗漏关键信息\n", "   - 提高临床实用性\n", "\n", "3. **报告结构相似度**：\n", "   - 评估报告的组织结构\n", "   - 确保符合医学报告规范\n", "   - 提高可读性和专业性\n", "\n", "### 评估框架特点\n", "\n", "1. **综合性**：\n", "   - 整合多种评估指标\n", "   - 提供全面的性能评估\n", "   - 支持单样本和语料库级评估\n", "\n", "2. **可扩展性**：\n", "   - 易于添加新的评估指标\n", "   - 支持自定义评估方法\n", "   - 适应不同的应用场景\n", "\n", "3. **可视化**：\n", "   - 提供直观的结果展示\n", "   - 支持多维度对比分析\n", "   - 便于结果解释和报告\n", "\n", "### 实际应用建议\n", "\n", "1. **指标选择**：\n", "   - 根据任务特点选择合适指标\n", "   - 平衡通用性和专业性\n", "   - 考虑计算效率和解释性\n", "\n", "2. **阈值设定**：\n", "   - 基于领域专家意见设定阈值\n", "   - 考虑临床应用的安全性\n", "   - 定期更新和校准标准\n", "\n", "3. **持续改进**：\n", "   - 定期评估和更新指标\n", "   - 收集用户反馈\n", "   - 结合人工评估验证\n", "\n", "### 下一步\n", "\n", "在下一个notebook中，我们将学习结果可视化和分析方法，了解如何深入分析模型性能和发现改进方向。\n", "\n", "### 关键收获\n", "\n", "- 文本生成评估需要多维度综合考虑\n", "- 医学报告有其特殊的评估需求\n", "- 自动评估指标需要与人工评估相结合\n", "- 完整的评估框架有助于模型改进\n", "- 可视化分析能够提供深入洞察"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}