{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 07 - 损失函数与优化\n", "\n", "在这个notebook中，我们将深入学习深度学习中的损失函数和优化策略，这是训练神经网络的核心组件。\n", "\n", "## 学习目标\n", "\n", "- 理解损失函数在深度学习中的作用\n", "- 掌握交叉熵损失函数的原理和实现\n", "- 学习梯度下降和Adam优化器\n", "- 理解学习率调度策略\n", "- 掌握梯度裁剪技术\n", "- 实现完整的优化流程\n", "- 可视化训练过程\n", "\n", "## 损失函数基础\n", "\n", "损失函数（Loss Function）是深度学习的核心概念之一，它：\n", "\n", "1. **衡量预测与真实值的差距**：量化模型预测的好坏\n", "2. **提供优化方向**：通过梯度指导参数更新\n", "3. **连接任务目标与数学优化**：将实际问题转化为数学优化问题\n", "\n", "### 常见损失函数类型\n", "\n", "- **回归任务**：均方误差(MSE)、平均绝对误差(MAE)\n", "- **分类任务**：交叉熵损失、Focal Loss\n", "- **序列生成**：交叉熵损失（逐词预测）\n", "- **对比学习**：对比损失、三元组损失\n", "\n", "### 医学报告生成中的损失函数\n", "\n", "在医学报告生成任务中，我们主要使用：\n", "- **交叉熵损失**：用于词汇预测\n", "- **序列级损失**：考虑整个序列的质量\n", "- **注意力正则化**：鼓励合理的注意力分布"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 导入必要的库\n", "import os\n", "import sys\n", "import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "import torch.optim as optim\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import math\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# 设置项目路径\n", "project_root = os.path.abspath('..')\n", "sys.path.append(project_root)\n", "\n", "print(f\"项目根目录: {project_root}\")\n", "print(f\"PyTorch版本: {torch.__version__}\")\n", "\n", "# 设置设备\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f\"使用设备: {device}\")\n", "\n", "# 设置随机种子\n", "torch.manual_seed(42)\n", "np.random.seed(42)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 交叉熵损失函数详解\n", "\n", "交叉熵损失是序列生成任务中最常用的损失函数。让我们从数学原理开始理解：\n", "\n", "### 数学原理\n", "\n", "对于单个样本的交叉熵损失：\n", "$$L = -\\sum_{i=1}^{V} y_i \\log(\\hat{y}_i)$$\n", "\n", "其中：\n", "- $V$：词汇表大小\n", "- $y_i$：真实标签（one-hot编码）\n", "- $\\hat{y}_i$：预测概率\n", "\n", "对于序列生成，总损失是所有时间步损失的平均：\n", "$$L_{total} = \\frac{1}{T} \\sum_{t=1}^{T} L_t$$"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class CrossEntropyLossExplained:\n", "    \"\"\"\n", "    交叉熵损失函数的详细实现和解释\n", "    \"\"\"\n", "    \n", "    @staticmethod\n", "    def manual_cross_entropy(predictions, targets):\n", "        \"\"\"\n", "        手动实现交叉熵损失（用于理解）\n", "        \n", "        Args:\n", "            predictions (torch.Tensor): 预测logits (batch_size, vocab_size)\n", "            targets (torch.Tensor): 真实标签 (batch_size,)\n", "        \n", "        Returns:\n", "            torch.Tensor: 损失值\n", "        \"\"\"\n", "        # 1. 计算softmax概率\n", "        probs = <PERSON>.softmax(predictions, dim=-1)\n", "        \n", "        # 2. 选择目标类别的概率\n", "        target_probs = probs.gather(1, targets.unsqueeze(1)).squeeze(1)\n", "        \n", "        # 3. 计算负对数似然\n", "        loss = -torch.log(target_probs + 1e-8)  # 加小值避免log(0)\n", "        \n", "        return loss.mean()\n", "    \n", "    @staticmethod\n", "    def demonstrate_cross_entropy():\n", "        \"\"\"\n", "        演示交叉熵损失的计算过程\n", "        \"\"\"\n", "        print(\"=== 交叉熵损失演示 ===\")\n", "        \n", "        # 创建示例数据\n", "        batch_size = 4\n", "        vocab_size = 10\n", "        \n", "        # 模型预测（logits）\n", "        predictions = torch.randn(batch_size, vocab_size)\n", "        # 真实标签\n", "        targets = torch.randint(0, vocab_size, (batch_size,))\n", "        \n", "        print(f\"批次大小: {batch_size}\")\n", "        print(f\"词汇表大小: {vocab_size}\")\n", "        print(f\"预测logits形状: {predictions.shape}\")\n", "        print(f\"目标标签: {targets.tolist()}\")\n", "        \n", "        # 计算概率\n", "        probs = <PERSON>.softmax(predictions, dim=-1)\n", "        print(f\"\\n预测概率形状: {probs.shape}\")\n", "        \n", "        # 手动计算损失\n", "        manual_loss = CrossEntropyLossExplained.manual_cross_entropy(predictions, targets)\n", "        \n", "        # PyTorch内置损失\n", "        criterion = nn.CrossEntropyLoss()\n", "        pytorch_loss = criterion(predictions, targets)\n", "        \n", "        print(f\"\\n手动计算损失: {manual_loss.item():.4f}\")\n", "        print(f\"PyTorch损失: {pytorch_loss.item():.4f}\")\n", "        print(f\"差异: {abs(manual_loss.item() - pytorch_loss.item()):.6f}\")\n", "        \n", "        # 可视化每个样本的损失\n", "        individual_losses = []\n", "        for i in range(batch_size):\n", "            target_prob = probs[i, targets[i]].item()\n", "            loss = -math.log(target_prob)\n", "            individual_losses.append(loss)\n", "            print(f\"样本{i+1}: 目标类别{targets[i]}, 预测概率{target_prob:.4f}, 损失{loss:.4f}\")\n", "        \n", "        # 可视化\n", "        fig, axes = plt.subplots(1, 2, figsize=(15, 5))\n", "        \n", "        # 预测概率分布\n", "        im = axes[0].imshow(probs.detach().numpy(), cmap='Blues', aspect='auto')\n", "        axes[0].set_title('预测概率分布')\n", "        axes[0].set_xlabel('词汇索引')\n", "        axes[0].set_ylabel('样本索引')\n", "        \n", "        # 标记目标类别\n", "        for i, target in enumerate(targets):\n", "            axes[0].scatter(target, i, color='red', s=100, marker='x')\n", "        \n", "        plt.colorbar(im, ax=axes[0])\n", "        \n", "        # 个体损失\n", "        axes[1].bar(range(batch_size), individual_losses, color='orange', alpha=0.7)\n", "        axes[1].set_title('各样本的交叉熵损失')\n", "        axes[1].set_xlabel('样本索引')\n", "        axes[1].set_ylabel('损失值')\n", "        axes[1].axhline(y=manual_loss.item(), color='red', linestyle='--', label='平均损失')\n", "        axes[1].legend()\n", "        \n", "        plt.tight_layout()\n", "        plt.show()\n", "        \n", "        return manual_loss, pytorch_loss\n", "\n", "# 运行交叉熵损失演示\n", "manual_loss, pytorch_loss = CrossEntropyLossExplained.demonstrate_cross_entropy()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 序列生成的损失函数\n", "\n", "在序列生成任务中，我们需要考虑特殊的处理方式："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class SequenceGenerationLoss(nn.Module):\n", "    \"\"\"\n", "    序列生成任务的损失函数\n", "    \n", "    处理填充标记、序列长度等特殊情况\n", "    \"\"\"\n", "    \n", "    def __init__(self, vocab_size, pad_idx=0, label_smoothing=0.0):\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        self.vocab_size = vocab_size\n", "        self.pad_idx = pad_idx\n", "        self.label_smoothing = label_smoothing\n", "        \n", "        # 忽略填充标记的交叉熵损失\n", "        self.criterion = nn.CrossEntropyLoss(\n", "            ignore_index=pad_idx, \n", "            label_smoothing=label_smoothing\n", "        )\n", "    \n", "    def forward(self, predictions, targets, mask=None):\n", "        \"\"\"\n", "        计算序列生成损失\n", "        \n", "        Args:\n", "            predictions (torch.Tensor): 预测logits (batch_size, seq_len, vocab_size)\n", "            targets (torch.Tensor): 目标序列 (batch_size, seq_len)\n", "            mask (torch.Tensor): 序列掩码 (batch_size, seq_len)\n", "        \n", "        Returns:\n", "            dict: 包含损失值和统计信息的字典\n", "        \"\"\"\n", "        batch_size, seq_len, vocab_size = predictions.shape\n", "        \n", "        # 重塑张量以适应CrossEntropyLoss\n", "        predictions_flat = predictions.view(-1, vocab_size)  # (batch_size * seq_len, vocab_size)\n", "        targets_flat = targets.view(-1)  # (batch_size * seq_len,)\n", "        \n", "        # 计算基础损失\n", "        loss = self.criterion(predictions_flat, targets_flat)\n", "        \n", "        # 如果提供了掩码，计算掩码损失\n", "        if mask is not None:\n", "            # 计算每个位置的损失\n", "            individual_losses = F.cross_entropy(\n", "                predictions_flat, targets_flat, \n", "                ignore_index=self.pad_idx, reduction='none'\n", "            )\n", "            individual_losses = individual_losses.view(batch_size, seq_len)\n", "            \n", "            # 应用掩码\n", "            masked_losses = individual_losses * mask.float()\n", "            \n", "            # 计算平均损失（只考虑非填充位置）\n", "            total_loss = masked_losses.sum()\n", "            total_tokens = mask.sum().float()\n", "            masked_loss = total_loss / (total_tokens + 1e-8)\n", "        else:\n", "            masked_loss = loss\n", "        \n", "        # 计算困惑度（Perplexity）\n", "        perplexity = torch.exp(loss)\n", "        \n", "        # 计算准确率\n", "        predictions_argmax = predictions.argmax(dim=-1)\n", "        if mask is not None:\n", "            correct = (predictions_argmax == targets) & mask.bool()\n", "            accuracy = correct.sum().float() / mask.sum().float()\n", "        else:\n", "            non_pad_mask = (targets != self.pad_idx)\n", "            correct = (predictions_argmax == targets) & non_pad_mask\n", "            accuracy = correct.sum().float() / non_pad_mask.sum().float()\n", "        \n", "        return {\n", "            'loss': loss,\n", "            'masked_loss': masked_loss,\n", "            'perplexity': perplexity,\n", "            'accuracy': accuracy\n", "        }\n", "\n", "def demonstrate_sequence_loss():\n", "    \"\"\"\n", "    演示序列生成损失函数\n", "    \"\"\"\n", "    print(\"\\n=== 序列生成损失演示 ===\")\n", "    \n", "    # 参数设置\n", "    batch_size = 3\n", "    seq_len = 8\n", "    vocab_size = 100\n", "    pad_idx = 0\n", "    \n", "    # 创建损失函数\n", "    loss_fn = SequenceGenerationLoss(vocab_size, pad_idx, label_smoothing=0.1)\n", "    \n", "    # 创建示例数据\n", "    predictions = torch.randn(batch_size, seq_len, vocab_size)\n", "    \n", "    # 创建目标序列（包含填充）\n", "    targets = torch.randint(1, vocab_size, (batch_size, seq_len))\n", "    # 添加一些填充标记\n", "    targets[0, 5:] = pad_idx  # 第一个序列在位置5后填充\n", "    targets[1, 6:] = pad_idx  # 第二个序列在位置6后填充\n", "    targets[2, 7:] = pad_idx  # 第三个序列在位置7后填充\n", "    \n", "    # 创建掩码\n", "    mask = (targets != pad_idx)\n", "    \n", "    print(f\"输入形状:\")\n", "    print(f\"  预测: {predictions.shape}\")\n", "    print(f\"  目标: {targets.shape}\")\n", "    print(f\"  掩码: {mask.shape}\")\n", "    \n", "    print(f\"\\n目标序列（0表示填充）:\")\n", "    for i in range(batch_size):\n", "        print(f\"  序列{i+1}: {targets[i].tolist()}\")\n", "    \n", "    # 计算损失\n", "    loss_info = loss_fn(predictions, targets, mask)\n", "    \n", "    print(f\"\\n损失统计:\")\n", "    print(f\"  基础损失: {loss_info['loss'].item():.4f}\")\n", "    print(f\"  掩码损失: {loss_info['masked_loss'].item():.4f}\")\n", "    print(f\"  困惑度: {loss_info['perplexity'].item():.4f}\")\n", "    print(f\"  准确率: {loss_info['accuracy'].item():.4f}\")\n", "    \n", "    # 可视化掩码效果\n", "    plt.figure(figsize=(12, 4))\n", "    \n", "    plt.subplot(1, 2, 1)\n", "    plt.imshow(mask.numpy(), cmap='Blues', aspect='auto')\n", "    plt.title('序列掩码\\n（蓝色=有效，白色=填充）')\n", "    plt.xlabel('序列位置')\n", "    plt.ylabel('批次索引')\n", "    plt.colorbar()\n", "    \n", "    plt.subplot(1, 2, 2)\n", "    # 计算每个位置的平均损失\n", "    position_losses = []\n", "    for pos in range(seq_len):\n", "        pos_predictions = predictions[:, pos, :]\n", "        pos_targets = targets[:, pos]\n", "        pos_mask = mask[:, pos]\n", "        \n", "        if pos_mask.sum() > 0:\n", "            pos_loss = F.cross_entropy(\n", "                pos_predictions[pos_mask], \n", "                pos_targets[pos_mask]\n", "            )\n", "            position_losses.append(pos_loss.item())\n", "        else:\n", "            position_losses.append(0)\n", "    \n", "    plt.bar(range(seq_len), position_losses, alpha=0.7)\n", "    plt.title('各位置的平均损失')\n", "    plt.xlabel('序列位置')\n", "    plt.ylabel('损失值')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    return loss_info\n", "\n", "# 运行序列损失演示\n", "seq_loss_info = demonstrate_sequence_loss()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 优化器详解\n", "\n", "优化器负责根据损失函数的梯度更新模型参数。让我们深入理解几种主要的优化算法：\n", "\n", "### 梯度下降算法族\n", "\n", "1. **SGD（随机梯度下降）**：$\\theta_{t+1} = \\theta_t - \\eta \\nabla L(\\theta_t)$\n", "2. **Momentum**：$v_{t+1} = \\beta v_t + \\nabla L(\\theta_t)$, $\\theta_{t+1} = \\theta_t - \\eta v_{t+1}$\n", "3. **Adam**：结合动量和自适应学习率\n", "\n", "### Adam优化器原理\n", "\n", "Adam（Adaptive Moment Estimation）是目前最流行的优化器，它：\n", "- 结合了动量（一阶矩估计）\n", "- 使用自适应学习率（二阶矩估计）\n", "- 对超参数相对不敏感"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class OptimizerComparison:\n", "    \"\"\"\n", "    比较不同优化器的性能\n", "    \"\"\"\n", "    \n", "    @staticmethod\n", "    def create_simple_model():\n", "        \"\"\"\n", "        创建一个简单的模型用于优化演示\n", "        \"\"\"\n", "        class SimpleModel(nn.Module):\n", "            def __init__(self, input_size, hidden_size, output_size):\n", "                super(<PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "                self.fc1 = nn.Linear(input_size, hidden_size)\n", "                self.fc2 = nn.Linear(hidden_size, hidden_size)\n", "                self.fc3 = nn.Linear(hidden_size, output_size)\n", "                self.relu = nn.ReLU()\n", "            \n", "            def forward(self, x):\n", "                x = self.relu(self.fc1(x))\n", "                x = self.relu(self.fc2(x))\n", "                x = self.fc3(x)\n", "                return x\n", "        \n", "        return SimpleModel(10, 20, 5)\n", "    \n", "    @staticmethod\n", "    def compare_optimizers():\n", "        \"\"\"\n", "        比较不同优化器的收敛性能\n", "        \"\"\"\n", "        print(\"\\n=== 优化器比较演示 ===\")\n", "        \n", "        # 创建数据\n", "        torch.manual_seed(42)\n", "        batch_size = 32\n", "        input_size = 10\n", "        output_size = 5\n", "        \n", "        X = torch.randn(batch_size, input_size)\n", "        y = torch.randint(0, output_size, (batch_size,))\n", "        \n", "        # 优化器配置\n", "        optimizers_config = {\n", "            'SGD': {'lr': 0.01},\n", "            'SGD+Momentum': {'lr': 0.01, 'momentum': 0.9},\n", "            'Adam': {'lr': 0.001},\n", "            'AdamW': {'lr': 0.001, 'weight_decay': 0.01}\n", "        }\n", "        \n", "        # 存储训练历史\n", "        training_history = {name: [] for name in optimizers_config.keys()}\n", "        \n", "        num_epochs = 100\n", "        \n", "        for opt_name, opt_config in optimizers_config.items():\n", "            print(f\"\\n训练使用 {opt_name}...\")\n", "            \n", "            # 创建新模型（确保公平比较）\n", "            model = OptimizerComparison.create_simple_model()\n", "            \n", "            # 创建优化器\n", "            if opt_name == 'SGD':\n", "                optimizer = optim.SGD(model.parameters(), **opt_config)\n", "            elif opt_name == 'SGD+Momentum':\n", "                optimizer = optim.SGD(model.parameters(), **opt_config)\n", "            elif opt_name == 'Adam':\n", "                optimizer = optim.Adam(model.parameters(), **opt_config)\n", "            elif opt_name == 'AdamW':\n", "                optimizer = optim.AdamW(model.parameters(), **opt_config)\n", "            \n", "            criterion = nn.CrossEntropyLoss()\n", "            \n", "            # 训练循环\n", "            for epoch in range(num_epochs):\n", "                optimizer.zero_grad()\n", "                outputs = model(X)\n", "                loss = criterion(outputs, y)\n", "                loss.backward()\n", "                optimizer.step()\n", "                \n", "                training_history[opt_name].append(loss.item())\n", "        \n", "        # 可视化比较结果\n", "        plt.figure(figsize=(12, 8))\n", "        \n", "        # 损失曲线\n", "        plt.subplot(2, 2, 1)\n", "        for opt_name, history in training_history.items():\n", "            plt.plot(history, label=opt_name, linewidth=2)\n", "        plt.title('训练损失比较')\n", "        plt.xlabel('Epoch')\n", "        plt.ylabel('Loss')\n", "        plt.legend()\n", "        plt.grid(True, alpha=0.3)\n", "        \n", "        # 对数尺度损失曲线\n", "        plt.subplot(2, 2, 2)\n", "        for opt_name, history in training_history.items():\n", "            plt.semilogy(history, label=opt_name, linewidth=2)\n", "        plt.title('训练损失比较（对数尺度）')\n", "        plt.xlabel('Epoch')\n", "        plt.ylabel('Loss (log scale)')\n", "        plt.legend()\n", "        plt.grid(True, alpha=0.3)\n", "        \n", "        # 最终损失比较\n", "        plt.subplot(2, 2, 3)\n", "        final_losses = [history[-1] for history in training_history.values()]\n", "        bars = plt.bar(training_history.keys(), final_losses, alpha=0.7)\n", "        plt.title('最终损失比较')\n", "        plt.ylabel('Final Loss')\n", "        plt.xticks(rotation=45)\n", "        \n", "        # 添加数值标签\n", "        for bar, loss in zip(bars, final_losses):\n", "            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,\n", "                    f'{loss:.4f}', ha='center', va='bottom')\n", "        \n", "        # 收敛速度比较（达到某个阈值的epoch数）\n", "        plt.subplot(2, 2, 4)\n", "        threshold = 0.5\n", "        convergence_epochs = []\n", "        \n", "        for opt_name, history in training_history.items():\n", "            # 找到第一次低于阈值的epoch\n", "            converged_epoch = next((i for i, loss in enumerate(history) if loss < threshold), num_epochs)\n", "            convergence_epochs.append(converged_epoch)\n", "        \n", "        bars = plt.bar(training_history.keys(), convergence_epochs, alpha=0.7, color='green')\n", "        plt.title(f'收敛速度比较\\n（达到损失<{threshold}的epoch数）')\n", "        plt.ylabel('Epochs to Converge')\n", "        plt.xticks(rotation=45)\n", "        \n", "        # 添加数值标签\n", "        for bar, epochs in zip(bars, convergence_epochs):\n", "            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,\n", "                    f'{epochs}', ha='center', va='bottom')\n", "        \n", "        plt.tight_layout()\n", "        plt.show()\n", "        \n", "        # 打印总结\n", "        print(f\"\\n优化器性能总结:\")\n", "        for i, (opt_name, history) in enumerate(training_history.items()):\n", "            final_loss = history[-1]\n", "            converged_epoch = convergence_epochs[i]\n", "            print(f\"  {opt_name:12s}: 最终损失={final_loss:.4f}, 收敛epoch={converged_epoch}\")\n", "        \n", "        return training_history\n", "\n", "# 运行优化器比较\n", "optimizer_history = OptimizerComparison.compare_optimizers()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 学习率调度策略\n", "\n", "学习率是优化过程中最重要的超参数之一。合适的学习率调度策略可以：\n", "- 加速收敛\n", "- 避免局部最优\n", "- 提高最终性能\n", "\n", "### 常见调度策略\n", "\n", "1. **固定学习率**：整个训练过程使用相同学习率\n", "2. **阶梯衰减**：每隔固定epoch降低学习率\n", "3. **指数衰减**：按指数函数衰减\n", "4. **余弦退火**：按余弦函数变化\n", "5. **Warmup**：开始时逐渐增加学习率"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class LearningRateScheduler:\n", "    \"\"\"\n", "    学习率调度策略演示\n", "    \"\"\"\n", "    \n", "    @staticmethod\n", "    def demonstrate_schedulers():\n", "        \"\"\"\n", "        演示不同的学习率调度策略\n", "        \"\"\"\n", "        print(\"\\n=== 学习率调度策略演示 ===\")\n", "        \n", "        # 创建简单模型\n", "        model = nn.<PERSON><PERSON>(10, 1)\n", "        \n", "        # 不同调度器配置\n", "        schedulers_config = {\n", "            'Fixed': None,\n", "            'StepLR': {'step_size': 30, 'gamma': 0.5},\n", "            'ExponentialLR': {'gamma': 0.95},\n", "            'CosineAnnealingLR': {'T_max': 100},\n", "            'ReduceLROnPlateau': {'patience': 10, 'factor': 0.5}\n", "        }\n", "        \n", "        num_epochs = 100\n", "        initial_lr = 0.1\n", "        \n", "        # 存储学习率历史\n", "        lr_history = {}\n", "        \n", "        for scheduler_name, config in schedulers_config.items():\n", "            # 重新初始化优化器\n", "            optimizer = optim.SGD(model.parameters(), lr=initial_lr)\n", "            \n", "            # 创建调度器\n", "            if scheduler_name == 'Fixed':\n", "                scheduler = None\n", "            elif scheduler_name == 'StepLR':\n", "                scheduler = optim.lr_scheduler.StepLR(optimizer, **config)\n", "            elif scheduler_name == 'ExponentialLR':\n", "                scheduler = optim.lr_scheduler.ExponentialLR(optimizer, **config)\n", "            elif scheduler_name == 'CosineAnnealingLR':\n", "                scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, **config)\n", "            elif scheduler_name == 'ReduceLROnPlateau':\n", "                scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, **config)\n", "            \n", "            # 记录学习率变化\n", "            lrs = []\n", "            for epoch in range(num_epochs):\n", "                current_lr = optimizer.param_groups[0]['lr']\n", "                lrs.append(current_lr)\n", "                \n", "                # 模拟训练步骤\n", "                if scheduler is not None:\n", "                    if scheduler_name == 'ReduceLROnPlateau':\n", "                        # 模拟损失（随机波动）\n", "                        fake_loss = 1.0 + 0.1 * np.sin(epoch * 0.1) + 0.05 * np.random.randn()\n", "                        scheduler.step(fake_loss)\n", "                    else:\n", "                        scheduler.step()\n", "            \n", "            lr_history[scheduler_name] = lrs\n", "        \n", "        # 可视化学习率变化\n", "        plt.figure(figsize=(15, 10))\n", "        \n", "        # 线性尺度\n", "        plt.subplot(2, 2, 1)\n", "        for scheduler_name, lrs in lr_history.items():\n", "            plt.plot(lrs, label=scheduler_name, linewidth=2)\n", "        plt.title('学习率调度策略比较（线性尺度）')\n", "        plt.xlabel('Epoch')\n", "        plt.ylabel('Learning Rate')\n", "        plt.legend()\n", "        plt.grid(True, alpha=0.3)\n", "        \n", "        # 对数尺度\n", "        plt.subplot(2, 2, 2)\n", "        for scheduler_name, lrs in lr_history.items():\n", "            plt.semilogy(lrs, label=scheduler_name, linewidth=2)\n", "        plt.title('学习率调度策略比较（对数尺度）')\n", "        plt.xlabel('Epoch')\n", "        plt.ylabel('Learning Rate (log scale)')\n", "        plt.legend()\n", "        plt.grid(True, alpha=0.3)\n", "        \n", "        # Warmup策略演示\n", "        plt.subplot(2, 2, 3)\n", "        warmup_epochs = 10\n", "        total_epochs = 100\n", "        max_lr = 0.001\n", "        \n", "        # 线性warmup + 余弦衰减\n", "        warmup_lrs = []\n", "        for epoch in range(total_epochs):\n", "            if epoch < warmup_epochs:\n", "                # 线性增加\n", "                lr = max_lr * (epoch + 1) / warmup_epochs\n", "            else:\n", "                # 余弦衰减\n", "                progress = (epoch - warmup_epochs) / (total_epochs - warmup_epochs)\n", "                lr = max_lr * 0.5 * (1 + np.cos(np.pi * progress))\n", "            warmup_lrs.append(lr)\n", "        \n", "        plt.plot(warmup_lrs, label='Warmup + Cosine', linewidth=2, color='red')\n", "        plt.axvline(x=warmup_epochs, color='red', linestyle='--', alpha=0.7, label='Warmup End')\n", "        plt.title('Warmup + 余弦衰减策略')\n", "        plt.xlabel('Epoch')\n", "        plt.ylabel('Learning Rate')\n", "        plt.legend()\n", "        plt.grid(True, alpha=0.3)\n", "        \n", "        # 学习率范围测试（LR Range Test）\n", "        plt.subplot(2, 2, 4)\n", "        min_lr = 1e-6\n", "        max_lr = 1e-1\n", "        num_steps = 100\n", "        \n", "        # 指数增长的学习率\n", "        lr_range = np.logspace(np.log10(min_lr), np.log10(max_lr), num_steps)\n", "        # 模拟对应的损失（通常先下降后上升）\n", "        simulated_loss = []\n", "        for i, lr in enumerate(lr_range):\n", "            # 模拟损失曲线：最优学习率附近损失最低\n", "            optimal_lr = 1e-3\n", "            distance = abs(np.log10(lr) - np.log10(optimal_lr))\n", "            loss = 1.0 + distance**2 + 0.1 * np.random.randn()\n", "            simulated_loss.append(loss)\n", "        \n", "        plt.semilogx(lr_range, simulated_loss, 'b-', linewidth=2)\n", "        plt.axvline(x=1e-3, color='red', linestyle='--', label='最优学习率')\n", "        plt.title('学习率范围测试')\n", "        plt.xlabel('Learning Rate (log scale)')\n", "        plt.ylabel('Loss')\n", "        plt.legend()\n", "        plt.grid(True, alpha=0.3)\n", "        \n", "        plt.tight_layout()\n", "        plt.show()\n", "        \n", "        # 打印调度策略总结\n", "        print(f\"\\n学习率调度策略总结:\")\n", "        for scheduler_name, lrs in lr_history.items():\n", "            initial = lrs[0]\n", "            final = lrs[-1]\n", "            min_lr = min(lrs)\n", "            max_lr = max(lrs)\n", "            print(f\"  {scheduler_name:15s}: 初始={initial:.6f}, 最终={final:.6f}, 范围=[{min_lr:.6f}, {max_lr:.6f}]\")\n", "        \n", "        return lr_history\n", "\n", "# 运行学习率调度演示\n", "lr_schedule_history = LearningRateScheduler.demonstrate_schedulers()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 梯度裁剪技术\n", "\n", "梯度裁剪是防止梯度爆炸的重要技术，特别在训练深度网络和RNN时非常有用。\n", "\n", "### 梯度裁剪方法\n", "\n", "1. **按值裁剪**：将梯度限制在[-clip_value, clip_value]范围内\n", "2. **按范数裁剪**：如果梯度范数超过阈值，则缩放梯度\n", "\n", "公式：$g' = \\min(1, \\frac{\\text{clip_norm}}{||g||}) \\cdot g$"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class GradientClippingDemo:\n", "    \"\"\"\n", "    梯度裁剪技术演示\n", "    \"\"\"\n", "    \n", "    @staticmethod\n", "    def demonstrate_gradient_clipping():\n", "        \"\"\"\n", "        演示梯度裁剪的效果\n", "        \"\"\"\n", "        print(\"\\n=== 梯度裁剪演示 ===\")\n", "        \n", "        # 创建一个容易产生梯度爆炸的模型\n", "        class ProblematicModel(nn.Module):\n", "            def __init__(self):\n", "                super(<PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "                # 使用较大的初始权重\n", "                self.layers = nn.ModuleList([\n", "                    nn.<PERSON><PERSON>(10, 50),\n", "                    nn.<PERSON><PERSON>(50, 50),\n", "                    nn.<PERSON><PERSON>(50, 50),\n", "                    nn.<PERSON><PERSON>(50, 1)\n", "                ])\n", "                \n", "                # 初始化为较大的权重\n", "                for layer in self.layers:\n", "                    nn.init.normal_(layer.weight, mean=0, std=2.0)\n", "            \n", "            def forward(self, x):\n", "                for layer in self.layers[:-1]:\n", "                    x = torch.tanh(layer(x))  # 使用tanh激活\n", "                x = self.layers[-1](x)\n", "                return x\n", "        \n", "        # 创建数据\n", "        torch.manual_seed(42)\n", "        X = torch.randn(32, 10)\n", "        y = torch.randn(32, 1)\n", "        \n", "        # 不同的梯度裁剪策略\n", "        clipping_strategies = {\n", "            'No Clipping': None,\n", "            'Clip by Value (0.5)': {'method': 'value', 'clip_value': 0.5},\n", "            'Clip by <PERSON><PERSON> (1.0)': {'method': 'norm', 'max_norm': 1.0},\n", "            'Clip by <PERSON><PERSON> (0.5)': {'method': 'norm', 'max_norm': 0.5}\n", "        }\n", "        \n", "        results = {}\n", "        num_epochs = 50\n", "        \n", "        for strategy_name, config in clipping_strategies.items():\n", "            print(f\"\\n训练使用策略: {strategy_name}\")\n", "            \n", "            # 创建新模型\n", "            model = ProblematicModel()\n", "            optimizer = optim.<PERSON>(model.parameters(), lr=0.01)\n", "            criterion = nn.MS<PERSON><PERSON>()\n", "            \n", "            # 记录训练历史\n", "            losses = []\n", "            grad_norms = []\n", "            \n", "            for epoch in range(num_epochs):\n", "                optimizer.zero_grad()\n", "                outputs = model(X)\n", "                loss = criterion(outputs, y)\n", "                loss.backward()\n", "                \n", "                # 计算梯度范数\n", "                total_norm = 0\n", "                for p in model.parameters():\n", "                    if p.grad is not None:\n", "                        param_norm = p.grad.data.norm(2)\n", "                        total_norm += param_norm.item() ** 2\n", "                total_norm = total_norm ** (1. / 2)\n", "                grad_norms.append(total_norm)\n", "                \n", "                # 应用梯度裁剪\n", "                if config is not None:\n", "                    if config['method'] == 'value':\n", "                        torch.nn.utils.clip_grad_value_(model.parameters(), config['clip_value'])\n", "                    elif config['method'] == 'norm':\n", "                        torch.nn.utils.clip_grad_norm_(model.parameters(), config['max_norm'])\n", "                \n", "                optimizer.step()\n", "                losses.append(loss.item())\n", "                \n", "                # 检查是否出现NaN\n", "                if torch.isnan(loss):\n", "                    print(f\"  在epoch {epoch}出现NaN，停止训练\")\n", "                    break\n", "            \n", "            results[strategy_name] = {\n", "                'losses': losses,\n", "                'grad_norms': grad_norms\n", "            }\n", "        \n", "        # 可视化结果\n", "        plt.figure(figsize=(15, 10))\n", "        \n", "        # 损失曲线\n", "        plt.subplot(2, 2, 1)\n", "        for strategy_name, data in results.items():\n", "            if data['losses']:\n", "                plt.plot(data['losses'], label=strategy_name, linewidth=2)\n", "        plt.title('训练损失比较')\n", "        plt.xlabel('Epoch')\n", "        plt.ylabel('Loss')\n", "        plt.legend()\n", "        plt.grid(True, alpha=0.3)\n", "        plt.yscale('log')\n", "        \n", "        # 梯度范数\n", "        plt.subplot(2, 2, 2)\n", "        for strategy_name, data in results.items():\n", "            if data['grad_norms']:\n", "                plt.plot(data['grad_norms'], label=strategy_name, linewidth=2)\n", "        plt.title('梯度范数变化')\n", "        plt.xlabel('Epoch')\n", "        plt.ylabel('Gradient Norm')\n", "        plt.legend()\n", "        plt.grid(True, alpha=0.3)\n", "        plt.yscale('log')\n", "        \n", "        # 梯度范数分布\n", "        plt.subplot(2, 2, 3)\n", "        for strategy_name, data in results.items():\n", "            if data['grad_norms']:\n", "                plt.hist(data['grad_norms'], bins=20, alpha=0.6, label=strategy_name)\n", "        plt.title('梯度范数分布')\n", "        plt.xlabel('Gradient Norm')\n", "        plt.ylabel('Frequency')\n", "        plt.legend()\n", "        plt.grid(True, alpha=0.3)\n", "        \n", "        # 最终性能比较\n", "        plt.subplot(2, 2, 4)\n", "        final_losses = []\n", "        strategy_names = []\n", "        \n", "        for strategy_name, data in results.items():\n", "            if data['losses'] and not np.isnan(data['losses'][-1]):\n", "                final_losses.append(data['losses'][-1])\n", "                strategy_names.append(strategy_name)\n", "        \n", "        if final_losses:\n", "            bars = plt.bar(range(len(strategy_names)), final_losses, alpha=0.7)\n", "            plt.xticks(range(len(strategy_names)), strategy_names, rotation=45)\n", "            plt.title('最终损失比较')\n", "            plt.ylabel('Final Loss')\n", "            \n", "            # 添加数值标签\n", "            for bar, loss in zip(bars, final_losses):\n", "                plt.text(bar.get_x() + bar.get_width()/2, bar.get_height(),\n", "                        f'{loss:.4f}', ha='center', va='bottom')\n", "        \n", "        plt.tight_layout()\n", "        plt.show()\n", "        \n", "        # 打印总结\n", "        print(f\"\\n梯度裁剪效果总结:\")\n", "        for strategy_name, data in results.items():\n", "            if data['losses']:\n", "                final_loss = data['losses'][-1] if data['losses'] else float('inf')\n", "                max_grad_norm = max(data['grad_norms']) if data['grad_norms'] else 0\n", "                avg_grad_norm = np.mean(data['grad_norms']) if data['grad_norms'] else 0\n", "                print(f\"  {strategy_name:20s}: 最终损失={final_loss:.4f}, 最大梯度范数={max_grad_norm:.2f}, 平均梯度范数={avg_grad_norm:.2f}\")\n", "        \n", "        return results\n", "\n", "# 运行梯度裁剪演示\n", "clipping_results = GradientClippingDemo.demonstrate_gradient_clipping()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 完整的优化流程\n", "\n", "现在让我们将所有组件整合，实现一个完整的优化流程："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class CompleteOptimizationPipeline:\n", "    \"\"\"\n", "    完整的优化流程实现\n", "    \n", "    集成损失函数、优化器、学习率调度和梯度裁剪\n", "    \"\"\"\n", "    \n", "    def __init__(self, model, train_loader, val_loader, config):\n", "        self.model = model\n", "        self.train_loader = train_loader\n", "        self.val_loader = val_loader\n", "        self.config = config\n", "        \n", "        # 设置损失函数\n", "        self.criterion = SequenceGenerationLoss(\n", "            vocab_size=config['vocab_size'],\n", "            pad_idx=config.get('pad_idx', 0),\n", "            label_smoothing=config.get('label_smoothing', 0.0)\n", "        )\n", "        \n", "        # 设置优化器\n", "        if config['optimizer'] == 'adam':\n", "            self.optimizer = optim.<PERSON>(\n", "                model.parameters(),\n", "                lr=config['learning_rate'],\n", "                weight_decay=config.get('weight_decay', 0.0)\n", "            )\n", "        elif config['optimizer'] == 'adamw':\n", "            self.optimizer = optim.AdamW(\n", "                model.parameters(),\n", "                lr=config['learning_rate'],\n", "                weight_decay=config.get('weight_decay', 0.01)\n", "            )\n", "        \n", "        # 设置学习率调度器\n", "        if config.get('scheduler') == 'cosine':\n", "            self.scheduler = optim.lr_scheduler.CosineAnnealingLR(\n", "                self.optimizer, T_max=config['num_epochs']\n", "            )\n", "        elif config.get('scheduler') == 'step':\n", "            self.scheduler = optim.lr_scheduler.StepLR(\n", "                self.optimizer, \n", "                step_size=config.get('step_size', 30),\n", "                gamma=config.get('gamma', 0.5)\n", "            )\n", "        else:\n", "            self.scheduler = None\n", "        \n", "        # 训练历史\n", "        self.history = {\n", "            'train_loss': [],\n", "            'val_loss': [],\n", "            'train_acc': [],\n", "            'val_acc': [],\n", "            'learning_rates': [],\n", "            'grad_norms': []\n", "        }\n", "    \n", "    def train_epoch(self):\n", "        \"\"\"\n", "        训练一个epoch\n", "        \"\"\"\n", "        self.model.train()\n", "        total_loss = 0\n", "        total_acc = 0\n", "        num_batches = 0\n", "        \n", "        for batch_idx, (inputs, targets, masks) in enumerate(self.train_loader):\n", "            # 前向传播\n", "            self.optimizer.zero_grad()\n", "            outputs = self.model(inputs)\n", "            \n", "            # 计算损失\n", "            loss_info = self.criterion(outputs, targets, masks)\n", "            loss = loss_info['loss']\n", "            \n", "            # 反向传播\n", "            loss.backward()\n", "            \n", "            # 梯度裁剪\n", "            if self.config.get('grad_clip_norm'):\n", "                grad_norm = torch.nn.utils.clip_grad_norm_(\n", "                    self.model.parameters(), \n", "                    self.config['grad_clip_norm']\n", "                )\n", "                self.history['grad_norms'].append(grad_norm.item())\n", "            \n", "            # 优化器步骤\n", "            self.optimizer.step()\n", "            \n", "            # 累计统计\n", "            total_loss += loss.item()\n", "            total_acc += loss_info['accuracy'].item()\n", "            num_batches += 1\n", "        \n", "        avg_loss = total_loss / num_batches\n", "        avg_acc = total_acc / num_batches\n", "        \n", "        return avg_loss, avg_acc\n", "    \n", "    def validate_epoch(self):\n", "        \"\"\"\n", "        验证一个epoch\n", "        \"\"\"\n", "        self.model.eval()\n", "        total_loss = 0\n", "        total_acc = 0\n", "        num_batches = 0\n", "        \n", "        with torch.no_grad():\n", "            for inputs, targets, masks in self.val_loader:\n", "                outputs = self.model(inputs)\n", "                loss_info = self.criterion(outputs, targets, masks)\n", "                \n", "                total_loss += loss_info['loss'].item()\n", "                total_acc += loss_info['accuracy'].item()\n", "                num_batches += 1\n", "        \n", "        avg_loss = total_loss / num_batches\n", "        avg_acc = total_acc / num_batches\n", "        \n", "        return avg_loss, avg_acc\n", "    \n", "    def train(self):\n", "        \"\"\"\n", "        完整的训练流程\n", "        \"\"\"\n", "        print(\"开始训练...\")\n", "        print(f\"配置: {self.config}\")\n", "        \n", "        for epoch in range(self.config['num_epochs']):\n", "            # 训练\n", "            train_loss, train_acc = self.train_epoch()\n", "            \n", "            # 验证\n", "            val_loss, val_acc = self.validate_epoch()\n", "            \n", "            # 学习率调度\n", "            if self.scheduler:\n", "                self.scheduler.step()\n", "            \n", "            # 记录历史\n", "            self.history['train_loss'].append(train_loss)\n", "            self.history['val_loss'].append(val_loss)\n", "            self.history['train_acc'].append(train_acc)\n", "            self.history['val_acc'].append(val_acc)\n", "            self.history['learning_rates'].append(self.optimizer.param_groups[0]['lr'])\n", "            \n", "            # 打印进度\n", "            if (epoch + 1) % 10 == 0:\n", "                print(f\"Epoch {epoch+1}/{self.config['num_epochs']}: \"\n", "                      f\"Train Loss={train_loss:.4f}, Val Loss={val_loss:.4f}, \"\n", "                      f\"Train Acc={train_acc:.4f}, Val Acc={val_acc:.4f}, \"\n", "                      f\"LR={self.optimizer.param_groups[0]['lr']:.6f}\")\n", "        \n", "        print(\"训练完成！\")\n", "        return self.history\n", "    \n", "    def plot_training_history(self):\n", "        \"\"\"\n", "        可视化训练历史\n", "        \"\"\"\n", "        fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "        \n", "        # 损失曲线\n", "        axes[0, 0].plot(self.history['train_loss'], label='Train Loss', linewidth=2)\n", "        axes[0, 0].plot(self.history['val_loss'], label='Val Loss', linewidth=2)\n", "        axes[0, 0].set_title('训练和验证损失')\n", "        axes[0, 0].set_xlabel('Epoch')\n", "        axes[0, 0].set_ylabel('Loss')\n", "        axes[0, 0].legend()\n", "        axes[0, 0].grid(True, alpha=0.3)\n", "        \n", "        # 准确率曲线\n", "        axes[0, 1].plot(self.history['train_acc'], label='Train Acc', linewidth=2)\n", "        axes[0, 1].plot(self.history['val_acc'], label='Val Acc', linewidth=2)\n", "        axes[0, 1].set_title('训练和验证准确率')\n", "        axes[0, 1].set_xlabel('Epoch')\n", "        axes[0, 1].set_ylabel('Accuracy')\n", "        axes[0, 1].legend()\n", "        axes[0, 1].grid(True, alpha=0.3)\n", "        \n", "        # 学习率变化\n", "        axes[1, 0].plot(self.history['learning_rates'], linewidth=2, color='green')\n", "        axes[1, 0].set_title('学习率变化')\n", "        axes[1, 0].set_xlabel('Epoch')\n", "        axes[1, 0].set_ylabel('Learning Rate')\n", "        axes[1, 0].grid(True, alpha=0.3)\n", "        \n", "        # 梯度范数\n", "        if self.history['grad_norms']:\n", "            axes[1, 1].plot(self.history['grad_norms'], linewidth=2, color='red')\n", "            axes[1, 1].set_title('梯度范数变化')\n", "            axes[1, 1].set_xlabel('Batch')\n", "            axes[1, 1].set_ylabel('Gradient Norm')\n", "            axes[1, 1].grid(True, alpha=0.3)\n", "        else:\n", "            axes[1, 1].text(0.5, 0.5, '未启用梯度裁剪', \n", "                           ha='center', va='center', transform=axes[1, 1].transAxes)\n", "            axes[1, 1].set_title('梯度范数变化')\n", "        \n", "        plt.tight_layout()\n", "        plt.show()\n", "\n", "print(\"\\n完整优化流程类已定义\")\n", "print(\"使用示例:\")\n", "print(\"config = {\")\n", "print(\"    'vocab_size': 1000,\")\n", "print(\"    'optimizer': 'adamw',\")\n", "print(\"    'learning_rate': 0.001,\")\n", "print(\"    'scheduler': 'cosine',\")\n", "print(\"    'grad_clip_norm': 1.0,\")\n", "print(\"    'num_epochs': 100\")\n", "print(\"}\")\n", "print(\"pipeline = CompleteOptimizationPipeline(model, train_loader, val_loader, config)\")\n", "print(\"history = pipeline.train()\")\n", "print(\"pipeline.plot_training_history()\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 小结\n", "\n", "在本节中，我们深入学习了深度学习中的损失函数和优化策略：\n", "\n", "### 核心概念\n", "\n", "1. **损失函数**：\n", "   - **交叉熵损失**：序列生成任务的标准损失函数\n", "   - **序列处理**：处理填充标记、变长序列等特殊情况\n", "   - **标签平滑**：提高模型泛化能力的技术\n", "\n", "2. **优化器**：\n", "   - **SGD**：基础的梯度下降算法\n", "   - **Adam/AdamW**：自适应学习率优化器，实际应用中的首选\n", "   - **动量机制**：加速收敛，避免局部最优\n", "\n", "3. **学习率调度**：\n", "   - **固定学习率**：简单但可能不是最优\n", "   - **衰减策略**：阶梯衰减、指数衰减、余弦退火\n", "   - **Warmup**：训练初期的学习率预热\n", "\n", "4. **梯度裁剪**：\n", "   - **按值裁剪**：限制梯度的绝对值\n", "   - **按范数裁剪**：限制梯度的L2范数\n", "   - **防止梯度爆炸**：特别重要在深度网络中\n", "\n", "### 实践要点\n", "\n", "1. **损失函数选择**：\n", "   - 序列生成任务使用交叉熵损失\n", "   - 正确处理填充标记\n", "   - 考虑使用标签平滑\n", "\n", "2. **优化器配置**：\n", "   - Adam/AdamW是大多数情况下的好选择\n", "   - 学习率通常在0.0001-0.01之间\n", "   - 权重衰减有助于正则化\n", "\n", "3. **学习率调度**：\n", "   - 余弦退火在很多任务中效果良好\n", "   - Warmup对大模型训练很重要\n", "   - 根据验证集性能调整策略\n", "\n", "4. **梯度裁剪**：\n", "   - 范数裁剪通常比值裁剪更有效\n", "   - 裁剪阈值通常在0.5-2.0之间\n", "   - 监控梯度范数变化\n", "\n", "### 医学报告生成中的应用\n", "\n", "在R2Gen等医学报告生成模型中：\n", "- 使用交叉熵损失进行词汇预测\n", "- AdamW优化器配合余弦学习率调度\n", "- 梯度裁剪防止训练不稳定\n", "- 标签平滑提高生成质量\n", "\n", "### 下一步\n", "\n", "在下一个notebook中，我们将学习如何实现完整的训练流程，包括数据加载、模型训练、验证和保存等完整的训练管道。\n", "\n", "### 关键收获\n", "\n", "- 损失函数是连接任务目标和数学优化的桥梁\n", "- 优化器的选择和配置对训练效果至关重要\n", "- 学习率调度可以显著提升模型性能\n", "- 梯度裁剪是训练稳定性的重要保障\n", "- 完整的优化流程需要综合考虑所有组件"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}