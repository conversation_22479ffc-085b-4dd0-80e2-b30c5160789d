{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 06 - 记忆驱动机制\n", "\n", "在这个notebook中，我们将深入学习R2Gen的核心创新：**关系记忆(Relational Memory)**机制。这是R2Gen相比标准Transformer的主要改进，能够显著提升医学报告生成的质量。\n", "\n", "## 学习目标\n", "\n", "- 理解记忆机制在序列生成中的重要性\n", "- 掌握关系记忆的基本原理和实现\n", "- 学习记忆的读取、写入和更新操作\n", "- 理解记忆如何增强Transformer的能力\n", "- 实现简化版的记忆驱动Transformer\n", "- 可视化记忆的工作过程\n", "\n", "## 为什么需要记忆机制？\n", "\n", "标准的Transformer虽然强大，但在某些任务中存在局限性：\n", "\n", "1. **有限的上下文窗口**：只能处理固定长度的序列\n", "2. **缺乏长期记忆**：无法保存和利用历史信息\n", "3. **重复生成问题**：容易生成重复或矛盾的内容\n", "4. **知识存储能力有限**：难以存储和检索大量事实知识\n", "\n", "### 医学报告生成中的挑战\n", "\n", "在医学报告生成任务中，这些问题尤为突出：\n", "- 需要记住已经描述过的解剖结构\n", "- 避免重复描述相同的发现\n", "- 保持报告的逻辑一致性\n", "- 利用医学知识库中的信息\n", "\n", "## 关系记忆机制原理\n", "\n", "关系记忆机制的核心思想是：\n", "\n", "1. **外部记忆存储**：维护一个外部的记忆矩阵\n", "2. **动态读写**：在生成过程中动态读取和更新记忆\n", "3. **关系建模**：记忆槽之间可以建立关系\n", "4. **注意力访问**：通过注意力机制访问记忆内容"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 导入必要的库\n", "import os\n", "import sys\n", "import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import math\n", "import copy\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# 设置项目路径\n", "project_root = os.path.abspath('..')\n", "sys.path.append(project_root)\n", "\n", "print(f\"项目根目录: {project_root}\")\n", "print(f\"PyTorch版本: {torch.__version__}\")\n", "\n", "# 设置设备\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f\"使用设备: {device}\")\n", "\n", "# 设置随机种子\n", "torch.manual_seed(42)\n", "np.random.seed(42)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 基础记忆模块实现\n", "\n", "首先，我们实现一个基础的记忆模块，理解记忆的基本操作："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class BasicMemoryModule(nn.Module):\n", "    \"\"\"\n", "    基础记忆模块\n", "    \n", "    实现记忆的基本操作：读取、写入、更新\n", "    \"\"\"\n", "    \n", "    def __init__(self, memory_size, memory_dim, query_dim):\n", "        \"\"\"\n", "        初始化记忆模块\n", "        \n", "        Args:\n", "            memory_size (int): 记忆槽数量\n", "            memory_dim (int): 每个记忆槽的维度\n", "            query_dim (int): 查询向量的维度\n", "        \"\"\"\n", "        super(BasicMemoryModule, self).__init__()\n", "        \n", "        self.memory_size = memory_size\n", "        self.memory_dim = memory_dim\n", "        self.query_dim = query_dim\n", "        \n", "        # 记忆矩阵：每行是一个记忆槽\n", "        self.memory = nn.Parameter(torch.randn(memory_size, memory_dim))\n", "        \n", "        # 查询投影层\n", "        self.query_proj = nn.Linear(query_dim, memory_dim)\n", "        \n", "        # 输出投影层\n", "        self.output_proj = nn.Linear(memory_dim, query_dim)\n", "        \n", "        # 门控机制（控制记忆更新）\n", "        self.gate = nn.Linear(query_dim + memory_dim, 1)\n", "        \n", "        print(f\"记忆模块初始化:\")\n", "        print(f\"  记忆槽数量: {memory_size}\")\n", "        print(f\"  记忆维度: {memory_dim}\")\n", "        print(f\"  查询维度: {query_dim}\")\n", "    \n", "    def read_memory(self, query):\n", "        \"\"\"\n", "        从记忆中读取信息\n", "        \n", "        Args:\n", "            query (torch.Tensor): 查询向量 (batch_size, query_dim)\n", "        \n", "        Returns:\n", "            tuple: (retrieved_memory, attention_weights)\n", "        \"\"\"\n", "        batch_size = query.size(0)\n", "        \n", "        # 投影查询向量到记忆空间\n", "        projected_query = self.query_proj(query)  # (batch_size, memory_dim)\n", "        \n", "        # 计算注意力分数\n", "        # query: (batch_size, memory_dim) -> (batch_size, 1, memory_dim)\n", "        # memory: (memory_size, memory_dim) -> (1, memory_size, memory_dim)\n", "        scores = torch.matmul(\n", "            projected_query.unsqueeze(1),  # (batch_size, 1, memory_dim)\n", "            self.memory.unsqueeze(0).transpose(-2, -1)  # (1, memory_dim, memory_size)\n", "        )  # (batch_size, 1, memory_size)\n", "        \n", "        scores = scores.squeeze(1)  # (batch_size, memory_size)\n", "        \n", "        # 计算注意力权重\n", "        attention_weights = F.softmax(scores, dim=-1)  # (batch_size, memory_size)\n", "        \n", "        # 加权求和得到检索的记忆\n", "        retrieved_memory = torch.matmul(\n", "            attention_weights.unsqueeze(1),  # (batch_size, 1, memory_size)\n", "            self.memory.unsqueeze(0).expand(batch_size, -1, -1)  # (batch_size, memory_size, memory_dim)\n", "        ).squeeze(1)  # (batch_size, memory_dim)\n", "        \n", "        return retrieved_memory, attention_weights\n", "    \n", "    def write_memory(self, query, retrieved_memory):\n", "        \"\"\"\n", "        更新记忆内容\n", "        \n", "        Args:\n", "            query (torch.Tensor): 查询向量 (batch_size, query_dim)\n", "            retrieved_memory (torch.Tensor): 检索的记忆 (batch_size, memory_dim)\n", "        \n", "        Returns:\n", "            torch.Tensor: 更新后的记忆矩阵\n", "        \"\"\"\n", "        batch_size = query.size(0)\n", "        \n", "        # 计算门控值（决定是否更新记忆）\n", "        gate_input = torch.cat([query, retrieved_memory], dim=-1)\n", "        gate_value = torch.sigmoid(self.gate(gate_input))  # (batch_size, 1)\n", "        \n", "        # 计算新的记忆内容\n", "        new_memory_content = self.query_proj(query)  # (batch_size, memory_dim)\n", "        \n", "        # 门控更新：new_memory = gate * new_content + (1 - gate) * old_memory\n", "        updated_memory = (\n", "            gate_value.unsqueeze(-1) * new_memory_content.unsqueeze(1) +\n", "            (1 - gate_value.unsqueeze(-1)) * retrieved_memory.unsqueeze(1)\n", "        )  # (batch_size, 1, memory_dim)\n", "        \n", "        return updated_memory.squeeze(1), gate_value\n", "    \n", "    def forward(self, query):\n", "        \"\"\"\n", "        前向传播：读取记忆并生成输出\n", "        \n", "        Args:\n", "            query (torch.Tensor): 查询向量 (batch_size, query_dim)\n", "        \n", "        Returns:\n", "            tuple: (output, attention_weights, gate_values)\n", "        \"\"\"\n", "        # 读取记忆\n", "        retrieved_memory, attention_weights = self.read_memory(query)\n", "        \n", "        # 更新记忆（可选）\n", "        updated_memory, gate_values = self.write_memory(query, retrieved_memory)\n", "        \n", "        # 生成输出\n", "        output = self.output_proj(retrieved_memory)\n", "        \n", "        return output, attention_weights, gate_values\n", "\n", "def demonstrate_basic_memory():\n", "    \"\"\"\n", "    演示基础记忆模块的工作原理\n", "    \"\"\"\n", "    print(\"\\n=== 基础记忆模块演示 ===\")\n", "    \n", "    # 参数设置\n", "    memory_size = 8\n", "    memory_dim = 64\n", "    query_dim = 128\n", "    batch_size = 3\n", "    \n", "    # 创建记忆模块\n", "    memory_module = BasicMemoryModule(memory_size, memory_dim, query_dim)\n", "    \n", "    # 创建查询\n", "    queries = torch.randn(batch_size, query_dim)\n", "    \n", "    print(f\"\\n输入查询形状: {queries.shape}\")\n", "    \n", "    # 前向传播\n", "    with torch.no_grad():\n", "        outputs, attention_weights, gate_values = memory_module(queries)\n", "    \n", "    print(f\"输出形状: {outputs.shape}\")\n", "    print(f\"注意力权重形状: {attention_weights.shape}\")\n", "    print(f\"门控值形状: {gate_values.shape}\")\n", "    \n", "    # 可视化注意力权重\n", "    plt.figure(figsize=(12, 4))\n", "    \n", "    for i in range(batch_size):\n", "        plt.subplot(1, batch_size, i + 1)\n", "        plt.bar(range(memory_size), attention_weights[i].numpy())\n", "        plt.title(f'样本 {i+1} 的记忆注意力')\n", "        plt.xlabel('记忆槽索引')\n", "        plt.ylabel('注意力权重')\n", "        plt.ylim(0, 1)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # 显示门控值\n", "    print(f\"\\n门控值（控制记忆更新）:\")\n", "    for i in range(batch_size):\n", "        print(f\"  样本 {i+1}: {gate_values[i].item():.3f}\")\n", "    \n", "    return memory_module, outputs, attention_weights\n", "\n", "# 运行基础记忆演示\n", "basic_memory, basic_outputs, basic_attention = demonstrate_basic_memory()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## R2Gen中的关系记忆机制\n", "\n", "R2Gen使用了更加复杂的关系记忆机制，它具有以下特点：\n", "\n", "1. **多头记忆注意力**：类似于多头注意力，使用多个记忆头\n", "2. **关系建模**：记忆槽之间可以建立关系\n", "3. **动态更新**：在解码过程中动态更新记忆内容\n", "4. **层次化记忆**：不同层的记忆存储不同级别的信息"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class RelationalMemory(nn.Module):\n", "    \"\"\"\n", "    关系记忆模块（基于R2Gen的设计）\n", "    \n", "    实现多头记忆注意力和关系建模\n", "    \"\"\"\n", "    \n", "    def __init__(self, num_slots, d_model, num_heads=8, dropout=0.1):\n", "        \"\"\"\n", "        初始化关系记忆\n", "        \n", "        Args:\n", "            num_slots (int): 记忆槽数量\n", "            d_model (int): 模型维度\n", "            num_heads (int): 注意力头数\n", "            dropout (float): Dropout率\n", "        \"\"\"\n", "        super(Relational<PERSON><PERSON><PERSON>, self).__init__()\n", "        \n", "        self.num_slots = num_slots\n", "        self.d_model = d_model\n", "        self.num_heads = num_heads\n", "        self.d_k = d_model // num_heads\n", "        \n", "        # 记忆矩阵\n", "        self.memory = nn.Parameter(torch.randn(num_slots, d_model))\n", "        \n", "        # 多头注意力的投影层\n", "        self.w_q = nn.Linear(d_model, d_model)\n", "        self.w_k = nn.Linear(d_model, d_model)\n", "        self.w_v = nn.Linear(d_model, d_model)\n", "        self.w_o = nn.Linear(d_model, d_model)\n", "        \n", "        # 记忆更新的门控机制\n", "        self.update_gate = nn.Linear(d_model * 2, d_model)\n", "        self.reset_gate = nn.Linear(d_model * 2, d_model)\n", "        self.new_gate = nn.Linear(d_model * 2, d_model)\n", "        \n", "        # 层归一化\n", "        self.layer_norm = nn.LayerNorm(d_model)\n", "        \n", "        self.dropout = nn.Dropout(dropout)\n", "        \n", "        print(f\"关系记忆初始化:\")\n", "        print(f\"  记忆槽数量: {num_slots}\")\n", "        print(f\"  模型维度: {d_model}\")\n", "        print(f\"  注意力头数: {num_heads}\")\n", "    \n", "    def multi_head_attention(self, query, key, value):\n", "        \"\"\"\n", "        多头注意力机制\n", "        \n", "        Args:\n", "            query (torch.Tensor): 查询 (batch_size, seq_len, d_model)\n", "            key (torch.Tensor): 键 (batch_size, num_slots, d_model)\n", "            value (torch.Tensor): 值 (batch_size, num_slots, d_model)\n", "        \n", "        Returns:\n", "            tuple: (output, attention_weights)\n", "        \"\"\"\n", "        batch_size, seq_len = query.size(0), query.size(1)\n", "        \n", "        # 线性变换\n", "        Q = self.w_q(query).view(batch_size, seq_len, self.num_heads, self.d_k).transpose(1, 2)\n", "        K = self.w_k(key).view(batch_size, self.num_slots, self.num_heads, self.d_k).transpose(1, 2)\n", "        V = self.w_v(value).view(batch_size, self.num_slots, self.num_heads, self.d_k).transpose(1, 2)\n", "        \n", "        # 计算注意力分数\n", "        scores = torch.matmul(Q, K.transpose(-2, -1)) / math.sqrt(self.d_k)\n", "        attention_weights = F.softmax(scores, dim=-1)\n", "        attention_weights = self.dropout(attention_weights)\n", "        \n", "        # 应用注意力权重\n", "        context = torch.matmul(attention_weights, V)\n", "        \n", "        # 拼接多头输出\n", "        context = context.transpose(1, 2).contiguous().view(\n", "            batch_size, seq_len, self.d_model\n", "        )\n", "        \n", "        # 最终线性变换\n", "        output = self.w_o(context)\n", "        \n", "        return output, attention_weights\n", "    \n", "    def update_memory(self, memory, new_info):\n", "        \"\"\"\n", "        使用GRU风格的门控机制更新记忆\n", "        \n", "        Args:\n", "            memory (torch.Tensor): 当前记忆 (batch_size, num_slots, d_model)\n", "            new_info (torch.Tensor): 新信息 (batch_size, num_slots, d_model)\n", "        \n", "        Returns:\n", "            torch.Tensor: 更新后的记忆\n", "        \"\"\"\n", "        # 拼接当前记忆和新信息\n", "        combined = torch.cat([memory, new_info], dim=-1)\n", "        \n", "        # 计算门控值\n", "        update_gate = torch.sigmoid(self.update_gate(combined))\n", "        reset_gate = torch.sigmoid(self.reset_gate(combined))\n", "        \n", "        # 计算候选新记忆\n", "        reset_memory = reset_gate * memory\n", "        candidate_memory = torch.tanh(self.new_gate(\n", "            torch.cat([reset_memory, new_info], dim=-1)\n", "        ))\n", "        \n", "        # 更新记忆\n", "        new_memory = (1 - update_gate) * memory + update_gate * candidate_memory\n", "        \n", "        return new_memory\n", "    \n", "    def forward(self, query, update_memory=True):\n", "        \"\"\"\n", "        前向传播\n", "        \n", "        Args:\n", "            query (torch.Tensor): 查询向量 (batch_size, seq_len, d_model)\n", "            update_memory (bool): 是否更新记忆\n", "        \n", "        Returns:\n", "            tuple: (output, attention_weights, updated_memory)\n", "        \"\"\"\n", "        batch_size = query.size(0)\n", "        \n", "        # 扩展记忆矩阵到批次维度\n", "        memory = self.memory.unsqueeze(0).expand(batch_size, -1, -1)\n", "        \n", "        # 多头注意力：查询记忆\n", "        attended_memory, attention_weights = self.multi_head_attention(\n", "            query, memory, memory\n", "        )\n", "        \n", "        # 残差连接和层归一化\n", "        output = self.layer_norm(query + attended_memory)\n", "        \n", "        # 更新记忆（如果需要）\n", "        updated_memory = memory\n", "        if update_memory:\n", "            # 使用查询信息更新记忆\n", "            query_for_update = query.mean(dim=1, keepdim=True).expand(-1, self.num_slots, -1)\n", "            updated_memory = self.update_memory(memory, query_for_update)\n", "            \n", "            # 更新全局记忆参数（在训练时）\n", "            if self.training:\n", "                self.memory.data = updated_memory.mean(dim=0)\n", "        \n", "        return output, attention_weights, updated_memory\n", "\n", "def demonstrate_relational_memory():\n", "    \"\"\"\n", "    演示关系记忆机制\n", "    \"\"\"\n", "    print(\"\\n=== 关系记忆机制演示 ===\")\n", "    \n", "    # 参数设置\n", "    num_slots = 6\n", "    d_model = 256\n", "    num_heads = 8\n", "    batch_size = 2\n", "    seq_len = 5\n", "    \n", "    # 创建关系记忆模块\n", "    rel_memory = RelationalMemory(num_slots, d_model, num_heads)\n", "    \n", "    # 创建查询序列\n", "    queries = torch.randn(batch_size, seq_len, d_model)\n", "    \n", "    print(f\"\\n输入查询形状: {queries.shape}\")\n", "    \n", "    # 前向传播\n", "    with torch.no_grad():\n", "        outputs, attention_weights, updated_memory = rel_memory(queries)\n", "    \n", "    print(f\"输出形状: {outputs.shape}\")\n", "    print(f\"注意力权重形状: {attention_weights.shape}\")\n", "    print(f\"更新记忆形状: {updated_memory.shape}\")\n", "    \n", "    # 可视化注意力权重（第一个头，第一个样本）\n", "    plt.figure(figsize=(12, 8))\n", "    \n", "    # 显示前4个注意力头的权重\n", "    for head in range(min(4, num_heads)):\n", "        plt.subplot(2, 2, head + 1)\n", "        attn_matrix = attention_weights[0, head].numpy()  # (seq_len, num_slots)\n", "        sns.heatmap(attn_matrix, annot=True, fmt='.2f', cmap='Blues',\n", "                   xticklabels=[f'M{i}' for i in range(num_slots)],\n", "                   yticklabels=[f'Q{i}' for i in range(seq_len)])\n", "        plt.title(f'注意力头 {head+1}')\n", "        plt.xlabel('记忆槽')\n", "        plt.ylabel('查询位置')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    return rel_memory, outputs, attention_weights\n", "\n", "# 运行关系记忆演示\n", "rel_memory, rel_outputs, rel_attention = demonstrate_relational_memory()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 记忆驱动的Transformer解码器\n", "\n", "现在我们将关系记忆集成到Transformer解码器中，创建一个记忆增强的解码器："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class MemoryAugmentedDecoderLayer(nn.Module):\n", "    \"\"\"\n", "    记忆增强的解码器层\n", "    \n", "    在标准Transformer解码器基础上添加记忆机制\n", "    \"\"\"\n", "    \n", "    def __init__(self, d_model, num_heads, d_ff, num_memory_slots, dropout=0.1):\n", "        super(MemoryAug<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        \n", "        self.d_model = d_model\n", "        \n", "        # 标准Transformer组件\n", "        self.self_attn = nn.MultiheadAttention(d_model, num_heads, dropout)\n", "        self.cross_attn = nn.MultiheadAttention(d_model, num_heads, dropout)\n", "        \n", "        # 记忆模块\n", "        self.memory = RelationalMemory(num_memory_slots, d_model, num_heads, dropout)\n", "        \n", "        # 前馈网络\n", "        self.feed_forward = nn.Sequential(\n", "            nn.<PERSON>ar(d_model, d_ff),\n", "            nn.ReLU(),\n", "            nn.Dropout(dropout),\n", "            nn.Linear(d_ff, d_model)\n", "        )\n", "        \n", "        # 层归一化\n", "        self.norm1 = nn.LayerNorm(d_model)\n", "        self.norm2 = nn.LayerNorm(d_model)\n", "        self.norm3 = nn.LayerNorm(d_model)\n", "        self.norm4 = nn.LayerNorm(d_model)\n", "        \n", "        self.dropout = nn.Dropout(dropout)\n", "    \n", "    def forward(self, tgt, memory_input, tgt_mask=None, memory_mask=None):\n", "        \"\"\"\n", "        前向传播\n", "        \n", "        Args:\n", "            tgt (torch.Tensor): 目标序列 (seq_len, batch_size, d_model)\n", "            memory_input (torch.Tensor): 编码器输出 (src_len, batch_size, d_model)\n", "            tgt_mask (torch.Tensor): 目标序列掩码\n", "            memory_mask (torch.Tensor): 记忆掩码\n", "        \n", "        Returns:\n", "            tuple: (output, memory_attention_weights)\n", "        \"\"\"\n", "        # 1. 自注意力\n", "        tgt2, _ = self.self_attn(tgt, tgt, tgt, attn_mask=tgt_mask)\n", "        tgt = self.norm1(tgt + self.dropout(tgt2))\n", "        \n", "        # 2. 编码器-解码器注意力\n", "        tgt2, _ = self.cross_attn(tgt, memory_input, memory_input, attn_mask=memory_mask)\n", "        tgt = self.norm2(tgt + self.dropout(tgt2))\n", "        \n", "        # 3. 记忆注意力\n", "        tgt_for_memory = tgt.transpose(0, 1)  # (batch_size, seq_len, d_model)\n", "        memory_output, memory_attn_weights, _ = self.memory(tgt_for_memory)\n", "        memory_output = memory_output.transpose(0, 1)  # (seq_len, batch_size, d_model)\n", "        tgt = self.norm3(tgt + self.dropout(memory_output))\n", "        \n", "        # 4. 前馈网络\n", "        tgt2 = self.feed_forward(tgt)\n", "        tgt = self.norm4(tgt + self.dropout(tgt2))\n", "        \n", "        return tgt, memory_attn_weights\n", "\n", "class MemoryAugmentedTransformer(nn.Module):\n", "    \"\"\"\n", "    记忆增强的Transformer模型\n", "    \n", "    结合了标准Transformer和关系记忆机制\n", "    \"\"\"\n", "    \n", "    def __init__(self, vocab_size, d_model=512, num_heads=8, num_layers=6, \n", "                 d_ff=2048, num_memory_slots=8, max_len=5000, dropout=0.1):\n", "        super(MemoryAugmentedTransformer, self).__init__()\n", "        \n", "        self.d_model = d_model\n", "        self.num_memory_slots = num_memory_slots\n", "        \n", "        # 词嵌入和位置编码\n", "        self.embedding = nn.Embedding(vocab_size, d_model)\n", "        self.pos_encoding = self._create_positional_encoding(max_len, d_model)\n", "        \n", "        # 解码器层\n", "        self.layers = nn.ModuleList([\n", "            MemoryAugmentedDecoderLayer(d_model, num_heads, d_ff, num_memory_slots, dropout)\n", "            for _ in range(num_layers)\n", "        ])\n", "        \n", "        # 输出投影\n", "        self.output_projection = nn.Linear(d_model, vocab_size)\n", "        \n", "        self.dropout = nn.Dropout(dropout)\n", "        \n", "        print(f\"记忆增强Transformer初始化:\")\n", "        print(f\"  词汇表大小: {vocab_size}\")\n", "        print(f\"  模型维度: {d_model}\")\n", "        print(f\"  层数: {num_layers}\")\n", "        print(f\"  记忆槽数量: {num_memory_slots}\")\n", "    \n", "    def _create_positional_encoding(self, max_len, d_model):\n", "        \"\"\"创建位置编码\"\"\"\n", "        pe = torch.zeros(max_len, d_model)\n", "        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)\n", "        div_term = torch.exp(torch.arange(0, d_model, 2).float() * \n", "                           (-math.log(10000.0) / d_model))\n", "        pe[:, 0::2] = torch.sin(position * div_term)\n", "        pe[:, 1::2] = torch.cos(position * div_term)\n", "        pe = pe.unsqueeze(0).transpose(0, 1)\n", "        self.register_buffer('pe', pe)\n", "        return pe\n", "    \n", "    def forward(self, tgt, memory, tgt_mask=None):\n", "        \"\"\"\n", "        前向传播\n", "        \n", "        Args:\n", "            tgt (torch.Tensor): 目标序列 (batch_size, tgt_len)\n", "            memory (torch.Tensor): 编码器输出 (batch_size, src_len, d_model)\n", "            tgt_mask (torch.Tensor): 目标序列掩码\n", "        \n", "        Returns:\n", "            tuple: (output, memory_attention_weights)\n", "        \"\"\"\n", "        seq_len = tgt.size(1)\n", "        \n", "        # 词嵌入和位置编码\n", "        tgt_embedded = self.embedding(tgt) * math.sqrt(self.d_model)\n", "        tgt_embedded = tgt_embedded.transpose(0, 1)  # (seq_len, batch_size, d_model)\n", "        tgt_embedded = tgt_embedded + self.pe[:seq_len, :]\n", "        tgt_embedded = self.dropout(tgt_embedded)\n", "        \n", "        # 准备编码器输出\n", "        memory = memory.transpose(0, 1)  # (src_len, batch_size, d_model)\n", "        \n", "        # 通过解码器层\n", "        output = tgt_embedded\n", "        all_memory_attention = []\n", "        \n", "        for layer in self.layers:\n", "            output, memory_attn = layer(output, memory, tgt_mask)\n", "            all_memory_attention.append(memory_attn)\n", "        \n", "        # 输出投影\n", "        output = output.transpose(0, 1)  # (batch_size, seq_len, d_model)\n", "        output = self.output_projection(output)\n", "        \n", "        return output, all_memory_attention\n", "\n", "def demonstrate_memory_transformer():\n", "    \"\"\"\n", "    演示记忆增强的Transformer\n", "    \"\"\"\n", "    print(\"\\n=== 记忆增强Transformer演示 ===\")\n", "    \n", "    # 参数设置\n", "    vocab_size = 1000\n", "    d_model = 256\n", "    num_heads = 8\n", "    num_layers = 3\n", "    d_ff = 1024\n", "    num_memory_slots = 6\n", "    \n", "    batch_size = 2\n", "    src_len = 10\n", "    tgt_len = 8\n", "    \n", "    # 创建模型\n", "    model = MemoryAugmentedTransformer(\n", "        vocab_size, d_model, num_heads, num_layers, \n", "        d_ff, num_memory_slots\n", "    )\n", "    \n", "    # 创建输入数据\n", "    tgt = torch.randint(0, vocab_size, (batch_size, tgt_len))\n", "    memory = torch.randn(batch_size, src_len, d_model)  # 编码器输出\n", "    \n", "    print(f\"\\n输入数据:\")\n", "    print(f\"  目标序列形状: {tgt.shape}\")\n", "    print(f\"  编码器输出形状: {memory.shape}\")\n", "    \n", "    # 前向传播\n", "    with torch.no_grad():\n", "        output, memory_attention = model(tgt, memory)\n", "    \n", "    print(f\"\\n输出:\")\n", "    print(f\"  输出形状: {output.shape}\")\n", "    print(f\"  记忆注意力层数: {len(memory_attention)}\")\n", "    \n", "    # 可视化第一层的记忆注意力\n", "    if memory_attention:\n", "        first_layer_attn = memory_attention[0]  # (batch_size, num_heads, seq_len, num_slots)\n", "        \n", "        plt.figure(figsize=(15, 5))\n", "        \n", "        # 显示前3个头的注意力\n", "        for head in range(min(3, num_heads)):\n", "            plt.subplot(1, 3, head + 1)\n", "            attn_matrix = first_layer_attn[0, head].numpy()  # (seq_len, num_slots)\n", "            sns.heatmap(attn_matrix, annot=True, fmt='.2f', cmap='Reds',\n", "                       xticklabels=[f'M{i}' for i in range(num_memory_slots)],\n", "                       yticklabels=[f'T{i}' for i in range(tgt_len)])\n", "            plt.title(f'记忆注意力头 {head+1}')\n", "            plt.xlabel('记忆槽')\n", "            plt.ylabel('目标位置')\n", "        \n", "        plt.tight_layout()\n", "        plt.show()\n", "    \n", "    # 计算模型参数\n", "    total_params = sum(p.numel() for p in model.parameters())\n", "    print(f\"\\n模型统计:\")\n", "    print(f\"  总参数数量: {total_params:,}\")\n", "    \n", "    return model, output, memory_attention\n", "\n", "# 运行记忆增强Transformer演示\n", "memory_transformer, mt_output, mt_attention = demonstrate_memory_transformer()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 记忆机制的优势分析\n", "\n", "让我们通过对比实验来分析记忆机制的优势："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def compare_with_without_memory():\n", "    \"\"\"\n", "    比较有记忆和无记忆的Transformer性能\n", "    \"\"\"\n", "    print(\"\\n=== 记忆机制优势分析 ===\")\n", "    \n", "    # 创建测试序列（模拟医学报告生成场景）\n", "    # 假设我们有一个重复模式的序列\n", "    vocab_size = 100\n", "    seq_len = 20\n", "    batch_size = 1\n", "    d_model = 128\n", "    \n", "    # 创建有记忆和无记忆的模型\n", "    model_with_memory = MemoryAugmentedTransformer(\n", "        vocab_size, d_model, num_heads=4, num_layers=2, \n", "        d_ff=512, num_memory_slots=4\n", "    )\n", "    \n", "    # 简化的无记忆模型（标准Transformer解码器）\n", "    class StandardTransformer(nn.Module):\n", "        def __init__(self, vocab_size, d_model):\n", "            super().__init__()\n", "            self.embedding = nn.Embedding(vocab_size, d_model)\n", "            self.decoder_layer = nn.TransformerDecoderLayer(d_model, 4, 512)\n", "            self.decoder = nn.TransformerDecoder(self.decoder_layer, 2)\n", "            self.output_proj = nn.Linear(d_model, vocab_size)\n", "        \n", "        def forward(self, tgt, memory):\n", "            tgt_emb = self.embedding(tgt).transpose(0, 1)\n", "            memory = memory.transpose(0, 1)\n", "            output = self.decoder(tgt_emb, memory)\n", "            return self.output_proj(output.transpose(0, 1))\n", "    \n", "    model_without_memory = StandardTransformer(vocab_size, d_model)\n", "    \n", "    # 创建测试数据\n", "    test_input = torch.randint(0, vocab_size, (batch_size, seq_len))\n", "    encoder_output = torch.randn(batch_size, 15, d_model)\n", "    \n", "    print(f\"测试配置:\")\n", "    print(f\"  序列长度: {seq_len}\")\n", "    print(f\"  词汇表大小: {vocab_size}\")\n", "    print(f\"  模型维度: {d_model}\")\n", "    \n", "    # 计算模型参数数量\n", "    params_with_memory = sum(p.numel() for p in model_with_memory.parameters())\n", "    params_without_memory = sum(p.numel() for p in model_without_memory.parameters())\n", "    \n", "    print(f\"\\n参数数量比较:\")\n", "    print(f\"  有记忆模型: {params_with_memory:,}\")\n", "    print(f\"  无记忆模型: {params_without_memory:,}\")\n", "    print(f\"  参数增加: {(params_with_memory - params_without_memory):,} ({(params_with_memory/params_without_memory - 1)*100:.1f}%)\")\n", "    \n", "    # 前向传播时间比较\n", "    import time\n", "    \n", "    # 有记忆模型\n", "    start_time = time.time()\n", "    with torch.no_grad():\n", "        output_with_memory, _ = model_with_memory(test_input, encoder_output)\n", "    time_with_memory = time.time() - start_time\n", "    \n", "    # 无记忆模型\n", "    start_time = time.time()\n", "    with torch.no_grad():\n", "        output_without_memory = model_without_memory(test_input, encoder_output)\n", "    time_without_memory = time.time() - start_time\n", "    \n", "    print(f\"\\n推理时间比较:\")\n", "    print(f\"  有记忆模型: {time_with_memory:.4f}秒\")\n", "    print(f\"  无记忆模型: {time_without_memory:.4f}秒\")\n", "    print(f\"  时间增加: {(time_with_memory/time_without_memory - 1)*100:.1f}%\")\n", "    \n", "    # 输出分析\n", "    print(f\"\\n输出分析:\")\n", "    print(f\"  有记忆模型输出形状: {output_with_memory.shape}\")\n", "    print(f\"  无记忆模型输出形状: {output_without_memory.shape}\")\n", "    \n", "    # 分析输出分布的差异\n", "    with_memory_probs = F.softmax(output_with_memory, dim=-1)\n", "    without_memory_probs = F.softmax(output_without_memory, dim=-1)\n", "    \n", "    # 计算熵（衡量输出的确定性）\n", "    entropy_with = -torch.sum(with_memory_probs * torch.log(with_memory_probs + 1e-8), dim=-1).mean()\n", "    entropy_without = -torch.sum(without_memory_probs * torch.log(without_memory_probs + 1e-8), dim=-1).mean()\n", "    \n", "    print(f\"\\n输出熵分析（越低越确定）:\")\n", "    print(f\"  有记忆模型熵: {entropy_with.item():.4f}\")\n", "    print(f\"  无记忆模型熵: {entropy_without.item():.4f}\")\n", "    \n", "    return {\n", "        'params_with': params_with_memory,\n", "        'params_without': params_without_memory,\n", "        'time_with': time_with_memory,\n", "        'time_without': time_without_memory,\n", "        'entropy_with': entropy_with.item(),\n", "        'entropy_without': entropy_without.item()\n", "    }\n", "\n", "# 运行比较分析\n", "comparison_results = compare_with_without_memory()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 记忆机制在医学报告生成中的应用\n", "\n", "让我们分析记忆机制如何具体帮助医学报告生成："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def analyze_memory_for_medical_reports():\n", "    \"\"\"\n", "    分析记忆机制在医学报告生成中的作用\n", "    \"\"\"\n", "    print(\"\\n=== 记忆机制在医学报告生成中的应用 ===\")\n", "    \n", "    # 模拟医学报告生成场景\n", "    medical_concepts = {\n", "        'anatomy': ['heart', 'lungs', 'liver', 'kidney', 'spine'],\n", "        'findings': ['normal', 'enlarged', 'clear', 'opacity', 'effusion'],\n", "        'locations': ['left', 'right', 'upper', 'lower', 'bilateral'],\n", "        'severity': ['mild', 'moderate', 'severe', 'stable', 'improved']\n", "    }\n", "    \n", "    print(\"医学报告生成中的挑战:\")\n", "    print(\"1. 避免重复描述相同的解剖结构\")\n", "    print(\"2. 保持发现的一致性\")\n", "    print(\"3. 记住已经提到的位置信息\")\n", "    print(\"4. 维护报告的逻辑结构\")\n", "    \n", "    print(\"\\n记忆机制的解决方案:\")\n", "    \n", "    # 1. 解剖结构记忆\n", "    print(\"\\n1. 解剖结构记忆:\")\n", "    print(\"   - 记忆槽1: 心脏相关信息\")\n", "    print(\"   - 记忆槽2: 肺部相关信息\")\n", "    print(\"   - 记忆槽3: 其他器官信息\")\n", "    print(\"   - 作用: 避免重复描述，确保完整性\")\n", "    \n", "    # 2. 发现记忆\n", "    print(\"\\n2. 发现记忆:\")\n", "    print(\"   - 记忆槽4: 正常发现\")\n", "    print(\"   - 记忆槽5: 异常发现\")\n", "    print(\"   - 作用: 保持发现的一致性，避免矛盾\")\n", "    \n", "    # 3. 位置记忆\n", "    print(\"\\n3. 位置记忆:\")\n", "    print(\"   - 记忆槽6: 左侧结构\")\n", "    print(\"   - 记忆槽7: 右侧结构\")\n", "    print(\"   - 作用: 确保位置描述的准确性\")\n", "    \n", "    # 4. 严重程度记忆\n", "    print(\"\\n4. 严重程度记忆:\")\n", "    print(\"   - 记忆槽8: 严重程度评估\")\n", "    print(\"   - 作用: 保持评估的一致性\")\n", "    \n", "    # 可视化记忆槽的专门化\n", "    plt.figure(figsize=(12, 8))\n", "    \n", "    # 创建模拟的记忆注意力模式\n", "    memory_patterns = {\n", "        '解剖结构词汇': [0.8, 0.7, 0.3, 0.1, 0.1, 0.2, 0.2, 0.1],\n", "        '发现描述词汇': [0.2, 0.2, 0.1, 0.9, 0.8, 0.1, 0.1, 0.2],\n", "        '位置描述词汇': [0.1, 0.1, 0.1, 0.1, 0.1, 0.9, 0.8, 0.1],\n", "        '严重程度词汇': [0.1, 0.1, 0.2, 0.2, 0.2, 0.1, 0.1, 0.9]\n", "    }\n", "    \n", "    # 绘制热图\n", "    patterns_matrix = np.array(list(memory_patterns.values()))\n", "    \n", "    sns.heatmap(patterns_matrix, \n", "                xticklabels=[f'记忆槽{i+1}' for i in range(8)],\n", "                yticklabels=list(memory_patterns.keys()),\n", "                annot=True, fmt='.1f', cmap='YlOrRd')\n", "    \n", "    plt.title('记忆槽的专门化模式\\n（不同类型词汇对不同记忆槽的注意力权重）')\n", "    plt.xlabel('记忆槽')\n", "    plt.ylabel('词汇类型')\n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    print(\"\\n记忆机制的优势:\")\n", "    print(\"✓ 减少重复内容\")\n", "    print(\"✓ 提高报告一致性\")\n", "    print(\"✓ 增强长期依赖建模\")\n", "    print(\"✓ 提供可解释的注意力模式\")\n", "    print(\"✓ 支持增量式报告生成\")\n", "    \n", "    return memory_patterns\n", "\n", "# 运行医学应用分析\n", "medical_memory_patterns = analyze_memory_for_medical_reports()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 小结\n", "\n", "在本节中，我们深入学习了R2Gen的核心创新：记忆驱动机制。\n", "\n", "### 核心概念\n", "\n", "1. **记忆机制的必要性**：\n", "   - 解决标准Transformer的上下文窗口限制\n", "   - 提供长期记忆能力\n", "   - 减少重复生成问题\n", "   - 增强知识存储和检索能力\n", "\n", "2. **关系记忆的特点**：\n", "   - **外部记忆存储**：独立于输入序列的记忆矩阵\n", "   - **多头记忆注意力**：并行访问不同类型的记忆信息\n", "   - **动态更新机制**：基于门控机制更新记忆内容\n", "   - **关系建模**：记忆槽之间可以建立复杂关系\n", "\n", "3. **记忆操作**：\n", "   - **读取**：通过注意力机制检索相关记忆\n", "   - **写入**：使用门控机制更新记忆内容\n", "   - **关联**：建立记忆槽之间的关系\n", "\n", "### 技术实现\n", "\n", "1. **基础记忆模块**：\n", "   - 记忆矩阵参数化\n", "   - 查询投影和输出投影\n", "   - 门控更新机制\n", "\n", "2. **关系记忆模块**：\n", "   - 多头注意力机制\n", "   - GRU风格的门控更新\n", "   - 层归一化和残差连接\n", "\n", "3. **记忆增强Transformer**：\n", "   - 集成记忆模块到解码器层\n", "   - 保持标准Transformer的其他组件\n", "   - 支持端到端训练\n", "\n", "### 在医学报告生成中的应用\n", "\n", "1. **解决特定挑战**：\n", "   - 避免重复描述解剖结构\n", "   - 保持发现描述的一致性\n", "   - 维护报告的逻辑结构\n", "   - 利用医学知识\n", "\n", "2. **记忆专门化**：\n", "   - 不同记忆槽存储不同类型的信息\n", "   - 解剖结构、发现、位置、严重程度等\n", "   - 提供可解释的注意力模式\n", "\n", "### 优势与代价\n", "\n", "**优势**：\n", "- 显著提升生成质量\n", "- 减少重复和矛盾\n", "- 增强长期依赖建模\n", "- 提供可解释性\n", "\n", "**代价**：\n", "- 增加模型参数（约10-20%）\n", "- 略微增加计算开销\n", "- 增加实现复杂度\n", "\n", "### 下一步\n", "\n", "在下一个notebook中，我们将学习损失函数和优化策略，了解如何有效训练这些复杂的记忆增强模型。\n", "\n", "### 关键收获\n", "\n", "- 记忆机制是R2Gen相比标准Transformer的核心改进\n", "- 关系记忆通过外部存储和动态更新提供长期记忆能力\n", "- 多头记忆注意力允许专门化的记忆槽\n", "- 记忆机制特别适合医学报告生成等需要一致性的任务\n", "- 记忆增强的代价是合理的，收益显著"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}