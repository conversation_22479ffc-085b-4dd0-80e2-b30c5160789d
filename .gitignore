# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# celery beat schedule file
celerybeat-schedule

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Machine Learning / Deep Learning
*.pth
*.pt
*.h5
*.hdf5
*.pkl
*.pickle
*.joblib
*.model
*.weights
checkpoints/
models/
saved_models/
logs/
runs/
tensorboard_logs/
wandb/

# Data files (但保留重要配置文件)
*.csv
R2Gen-main/data/
datasets/
iu_xray/images/
# 忽略特征文件
*.npy
two_stage_model/features/

# Results and outputs (但保留最终报告)
two_stage_model/results/
outputs/
output/
figures/
plots/
reports/

# 允许重要文件
!goal.md
!two_stage_model/*.md
!two_stage_model/*.json
!two_stage_model/*.html
!two_stage_model/*.png
!verified_technical_specs.json
!missing_features_extraction_results.json

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~
.project
.pydevproject

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
*.bak
*.backup

# Configuration files with sensitive data
config.ini
secrets.json
.env.local
.env.production

# Cache directories
.cache/
cache/
tmp/
temp/

# Node.js (if any frontend components)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Specific to this project
*.md~
