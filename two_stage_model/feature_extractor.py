#!/usr/bin/env python3
"""
R2Gen特征提取器

功能：
1. 加载预训练的R2Gen模型
2. 提取所有数据集样本的视觉特征
3. 保存特征为.npy文件，供两阶段训练使用

特征格式：
- att_feats: (98, 2048) - 注意力特征，98个区域
- fc_feats: (4096,) - 全连接特征，双图像拼接
"""

import sys
import os
sys.path.append('../R2Gen-main')

import torch
import numpy as np
import json
from tqdm import tqdm
import argparse
from pathlib import Path

# 导入R2Gen模块
from modules.tokenizers import Tokenizer
from modules.dataloaders import R2DataLoader
from models.r2gen import R2GenModel

class FeatureExtractor:
    """R2Gen特征提取器类"""
    
    def __init__(self, model_path=None, device='auto'):
        """
        初始化特征提取器
        
        Args:
            model_path (str): 预训练模型路径，如果为None则使用随机初始化的模型
            device (str): 计算设备，'auto'自动选择
        """
        self.device = self._setup_device(device)
        self.args = self._create_args()
        
        print("初始化特征提取器...")
        print(f"使用设备: {self.device}")
        
        # 创建tokenizer
        self.tokenizer = Tokenizer(self.args)
        print(f"词汇表大小: {len(self.tokenizer.token2idx)}")
        
        # 创建模型
        self.model = R2GenModel(self.args, self.tokenizer)
        
        # 加载预训练权重（如果提供）
        if model_path and os.path.exists(model_path):
            print(f"加载预训练模型: {model_path}")
            checkpoint = torch.load(model_path, map_location=self.device)
            self.model.load_state_dict(checkpoint['state_dict'])
        else:
            print("使用随机初始化的模型（用于测试）")
        
        self.model.to(self.device)
        self.model.eval()
        
        # 创建输出目录
        self.output_dir = Path('features')
        self.output_dir.mkdir(exist_ok=True)
        for split in ['train', 'val', 'test']:
            (self.output_dir / split).mkdir(exist_ok=True)
    
    def _setup_device(self, device):
        """设置计算设备"""
        if device == 'auto':
            return torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        return torch.device(device)
    
    def _create_args(self):
        """创建模型参数"""
        class Args:
            def __init__(self):
                # 数据设置
                self.image_dir = '../R2Gen-main/data/iu_xray/images/'
                self.ann_path = '../R2Gen-main/data/iu_xray/annotation.json'
                self.dataset_name = 'iu_xray'
                self.max_seq_length = 60
                self.threshold = 3
                self.num_workers = 2
                self.batch_size = 1  # 逐个处理样本
                
                # 模型设置
                self.visual_extractor = 'resnet101'
                self.visual_extractor_pretrained = True
                
                # Transformer设置
                self.d_model = 512
                self.d_ff = 512
                self.d_vf = 2048
                self.num_heads = 8
                self.num_layers = 3
                self.dropout = 0.1
                self.logit_layers = 1
                self.bos_idx = 0
                self.eos_idx = 0
                self.pad_idx = 0
                self.use_bn = 0
                self.drop_prob_lm = 0.5
                
                # 记忆模块设置
                self.rm_num_slots = 3
                self.rm_num_heads = 8
                self.rm_d_model = 512
                
                # 采样设置
                self.sample_method = 'beam_search'
                self.beam_size = 3
                self.temperature = 1.0
                self.sample_n = 1
                self.group_size = 1
                self.output_logsoftmax = 1
                self.decoding_constraint = 0
                self.block_trigrams = 1
        
        return Args()
    
    def extract_features_for_split(self, split='train'):
        """
        为指定数据分割提取特征
        
        Args:
            split (str): 数据分割 ('train', 'val', 'test')
        """
        print(f"\n开始提取 {split} 集特征...")
        
        # 创建数据加载器
        dataloader = R2DataLoader(self.args, self.tokenizer, split=split, shuffle=False)
        print(f"{split} 集样本数: {len(dataloader.dataset)}")
        
        # 统计信息
        total_samples = len(dataloader.dataset)
        processed_samples = 0
        failed_samples = []
        
        # 逐个处理样本
        with torch.no_grad():
            for batch_idx, batch in enumerate(tqdm(dataloader, desc=f"提取{split}特征")):
                try:
                    # 解析批次数据
                    images_id, images, reports_ids, reports_masks = batch
                    sample_id = images_id[0]  # batch_size=1，所以取第一个
                    
                    # 移动到设备
                    images = images.to(self.device)
                    
                    # 提取特征（按照IU X-ray的双图像处理逻辑）
                    att_feats_0, fc_feats_0 = self.model.visual_extractor(images[:, 0])
                    att_feats_1, fc_feats_1 = self.model.visual_extractor(images[:, 1])
                    
                    # 合并双图像特征
                    att_feats = torch.cat((att_feats_0, att_feats_1), dim=1)  # (1, 98, 2048)
                    fc_feats = torch.cat((fc_feats_0, fc_feats_1), dim=1)     # (1, 4096)
                    
                    # 验证特征形状
                    assert att_feats.shape == (1, 98, 2048), f"att_feats形状错误: {att_feats.shape}"
                    assert fc_feats.shape == (1, 4096), f"fc_feats形状错误: {fc_feats.shape}"
                    
                    # 保存特征
                    self._save_features(sample_id, att_feats, fc_feats, split)
                    
                    processed_samples += 1
                    
                except Exception as e:
                    print(f"\n处理样本 {sample_id} 时出错: {e}")
                    failed_samples.append(sample_id)
                    continue
        
        # 输出统计信息
        print(f"\n{split} 集特征提取完成:")
        print(f"  总样本数: {total_samples}")
        print(f"  成功处理: {processed_samples}")
        print(f"  失败样本: {len(failed_samples)}")
        
        if failed_samples:
            print(f"  失败样本ID: {failed_samples}")
        
        return processed_samples, failed_samples
    
    def _save_features(self, sample_id, att_feats, fc_feats, split):
        """
        保存特征到文件
        
        Args:
            sample_id (str): 样本ID
            att_feats (torch.Tensor): 注意力特征 (1, 98, 2048)
            fc_feats (torch.Tensor): 全连接特征 (1, 4096)
            split (str): 数据分割
        """
        # 移除batch维度并转换为numpy
        features = {
            'att_feats': att_feats.squeeze(0).cpu().numpy(),  # (98, 2048)
            'fc_feats': fc_feats.squeeze(0).cpu().numpy()     # (4096,)
        }
        
        # 保存文件
        feature_path = self.output_dir / split / f"{sample_id}.npy"
        np.save(feature_path, features)
    
    def extract_all_features(self):
        """提取所有数据集的特征"""
        print("=== R2Gen特征提取开始 ===")
        
        total_stats = {}
        
        for split in ['train', 'val', 'test']:
            processed, failed = self.extract_features_for_split(split)
            total_stats[split] = {
                'processed': processed,
                'failed': len(failed),
                'failed_ids': failed
            }
        
        # 保存统计信息
        stats_path = self.output_dir / 'extraction_stats.json'
        with open(stats_path, 'w') as f:
            json.dump(total_stats, f, indent=2)
        
        print(f"\n=== 特征提取完成 ===")
        print(f"统计信息已保存到: {stats_path}")
        
        return total_stats

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='R2Gen特征提取器')
    parser.add_argument('--model_path', type=str, default=None,
                       help='预训练模型路径')
    parser.add_argument('--device', type=str, default='auto',
                       help='计算设备 (auto/cpu/cuda)')
    parser.add_argument('--split', type=str, default='all',
                       choices=['all', 'train', 'val', 'test'],
                       help='要处理的数据分割')
    
    args = parser.parse_args()
    
    # 创建特征提取器
    extractor = FeatureExtractor(model_path=args.model_path, device=args.device)
    
    # 提取特征
    if args.split == 'all':
        extractor.extract_all_features()
    else:
        extractor.extract_features_for_split(args.split)

if __name__ == '__main__':
    main()
