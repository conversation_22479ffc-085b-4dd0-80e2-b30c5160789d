"""
R2Gen原始特征提取器

实现与原始R2Gen模型完全一致的特征提取器，包括：
1. 完全相同的数据预处理（随机裁切、随机翻转等）
2. 相同的ResNet101视觉特征提取器
3. 相同的特征拼接逻辑
4. 相同的训练/验证模式切换

⚠️ 关键要求：与原始R2Gen保持100%一致性
"""

import sys
import os
import torch
import torch.nn as nn
from torchvision import transforms
from typing import Dict, Tuple, Any, Union, List
from PIL import Image

# 添加R2Gen-main路径以导入原始模块
sys.path.append(os.path.join(os.path.dirname(__file__), '../../R2Gen-main'))

from .base_extractor import BaseFeatureExtractor


class R2GenExtractor(BaseFeatureExtractor):
    """
    R2Gen原始特征提取器（baseline）
    
    ⚠️ 关键要求：与原始R2Gen保持100%一致性
    - 完全相同的数据预处理（随机裁切、随机翻转等）
    - 相同的视觉特征提取器（ResNet101）
    - 相同的特征拼接逻辑
    - 相同的训练/验证模式切换
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化R2Gen特征提取器
        
        Args:
            config: 配置字典，包含：
                - name: 提取器名称
                - pretrained_path: 预训练模型路径（可选）
                - device: 设备类型
        """
        super().__init__(config)
        
        # 加载R2Gen原始视觉提取器
        self.visual_extractor = self._load_r2gen_extractor()
        
        # 设置与R2Gen完全一致的数据预处理
        self.train_transform = transforms.Compose([
            transforms.Resize(256),                    # 与R2Gen一致
            transforms.RandomCrop(224),                # 随机裁切
            transforms.RandomHorizontalFlip(),         # 随机水平翻转
            transforms.ToTensor(),
            transforms.Normalize((0.485, 0.456, 0.406),
                               (0.229, 0.224, 0.225))  # ImageNet标准化
        ])
        
        self.val_transform = transforms.Compose([
            transforms.Resize((224, 224)),             # 验证时固定尺寸
            transforms.ToTensor(),
            transforms.Normalize((0.485, 0.456, 0.406),
                               (0.229, 0.224, 0.225))
        ])
        
        # 移动到指定设备
        self.to(self.device)
        
    def _load_r2gen_extractor(self):
        """
        加载R2Gen原始视觉提取器
        
        Returns:
            VisualExtractor: R2Gen原始视觉提取器实例
        """
        try:
            from modules.visual_extractor import VisualExtractor
        except ImportError:
            raise ImportError("无法导入R2Gen的VisualExtractor，请确保R2Gen-main目录存在且路径正确")
        
        # 创建与R2Gen完全一致的参数
        class R2GenArgs:
            visual_extractor = 'resnet101'
            visual_extractor_pretrained = True
            
        args = R2GenArgs()
        extractor = VisualExtractor(args)
        
        # 如果有预训练权重，加载它们
        if 'pretrained_path' in self.config and self.config['pretrained_path']:
            print(f"加载预训练权重: {self.config['pretrained_path']}")
            checkpoint = torch.load(self.config['pretrained_path'], map_location='cpu')
            
            # 只加载视觉提取器的权重
            visual_state_dict = {}
            for k, v in checkpoint.items():
                if k.startswith('visual_extractor.'):
                    new_key = k.replace('visual_extractor.', '')
                    visual_state_dict[new_key] = v
            
            if visual_state_dict:
                extractor.load_state_dict(visual_state_dict, strict=False)
                print("成功加载视觉提取器权重")
            else:
                print("警告：未找到视觉提取器权重，使用ImageNet预训练权重")
                
        return extractor
        
    def extract_features(self, images: Union[torch.Tensor, List], mode: str = 'train') -> Dict[str, torch.Tensor]:
        """
        与R2Gen完全一致的特征提取
        
        Args:
            images: 输入图像
                - 如果是torch.Tensor: (batch_size, 2, 3, 224, 224) for IU X-ray双图像
                - 如果是List: 包含PIL图像对的列表
            mode: 'train' 或 'val'，决定使用哪种预处理
            
        Returns:
            Dict[str, torch.Tensor]: 标准化特征字典
                - 'att_feats': 注意力特征 (batch_size, 98, 2048)
                - 'fc_feats': 全连接特征 (batch_size, 4096)
                - 'metadata': 提取器元信息
        """
        # 如果输入是PIL图像列表，进行预处理
        if isinstance(images, list):
            transform = self.train_transform if mode == 'train' else self.val_transform
            processed_images = []
            
            for img_pair in images:  # 每个样本包含两张图像
                if isinstance(img_pair, (list, tuple)) and len(img_pair) == 2:
                    img1 = transform(img_pair[0])
                    img2 = transform(img_pair[1])
                    processed_images.append(torch.stack([img1, img2]))
                else:
                    raise ValueError("每个样本必须包含两张图像（IU X-ray数据集）")
            
            images = torch.stack(processed_images).to(self.device)
        
        # 确保输入格式正确
        if len(images.shape) != 5 or images.shape[1] != 2:
            raise ValueError(f"输入图像格式错误，期望 (batch_size, 2, 3, 224, 224)，得到 {images.shape}")
        
        # 设置模型为适当模式
        if mode == 'train':
            self.visual_extractor.train()
        else:
            self.visual_extractor.eval()
        
        with torch.no_grad() if mode == 'val' else torch.enable_grad():
            # 分别提取双图像特征（与R2Gen forward_iu_xray完全一致）
            att_feats_0, fc_feats_0 = self.visual_extractor(images[:, 0])  # 第一张图
            att_feats_1, fc_feats_1 = self.visual_extractor(images[:, 1])  # 第二张图
            
            # 拼接特征（顺序和方式必须与R2Gen一致）
            fc_feats = torch.cat((fc_feats_0, fc_feats_1), dim=1)    # (B, 4096)
            att_feats = torch.cat((att_feats_0, att_feats_1), dim=1) # (B, 98, 2048)
        
        # 构建返回结果
        features = {
            'att_feats': att_feats,
            'fc_feats': fc_feats,
            'metadata': {
                'extractor': 'r2gen_baseline',
                'mode': mode,
                'feature_dim': self.get_feature_dim(),
                'preprocessing': 'r2gen_original',
                'batch_size': images.shape[0]
            }
        }
        
        # 验证输出格式
        self._validate_output_format(features)
        
        return features
        
    def get_feature_dim(self) -> Dict[str, Tuple]:
        """
        返回特征维度信息
        
        Returns:
            Dict[str, Tuple]: 特征维度字典
        """
        return {
            'att_feats': (98, 2048),  # 双图像拼接后的注意力特征
            'fc_feats': (4096,),      # 双图像拼接后的全连接特征
            'visual_backbone': 'resnet101'
        }
    
    def to(self, device: torch.device):
        """
        将提取器移动到指定设备
        
        Args:
            device: 目标设备
        """
        super().to(device)
        if hasattr(self, 'visual_extractor'):
            self.visual_extractor.to(device)
        return self
    
    def eval(self):
        """设置为评估模式"""
        super().eval()
        if hasattr(self, 'visual_extractor'):
            self.visual_extractor.eval()
        return self
    
    def train(self):
        """设置为训练模式"""
        super().train()
        if hasattr(self, 'visual_extractor'):
            self.visual_extractor.train()
        return self
