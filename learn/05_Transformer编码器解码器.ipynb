{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 05 - Transformer编码器解码器\n", "\n", "在这个notebook中，我们将深入学习Transformer架构，这是R2Gen项目中用于将视觉特征转换为文本描述的核心组件。\n", "\n", "## 学习目标\n", "\n", "- 理解Transformer的基本原理和架构\n", "- 掌握自注意力机制的工作原理\n", "- 学习多头注意力和位置编码\n", "- 理解编码器-解码器架构\n", "- 实现简化版的Transformer组件\n", "- 可视化注意力权重\n", "\n", "## Transformer简介\n", "\n", "Transformer是一种基于注意力机制的神经网络架构，由Vaswani等人在2017年提出。它的核心创新是：\n", "\n", "1. **完全基于注意力**：不使用循环或卷积结构\n", "2. **并行计算**：可以并行处理序列中的所有位置\n", "3. **长距离依赖**：能够有效捕获长距离的依赖关系\n", "4. **可解释性**：注意力权重提供了模型决策的可解释性\n", "\n", "### Transformer架构概览\n", "\n", "```\n", "输入序列 -> 编码器 -> 上下文表示 -> 解码器 -> 输出序列\n", "```\n", "\n", "- **编码器**：将输入序列编码为上下文表示\n", "- **解码器**：基于上下文表示生成输出序列\n", "- **注意力机制**：允许模型关注输入的不同部分"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 导入必要的库\n", "import os\n", "import sys\n", "import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import math\n", "import copy\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# 设置项目路径\n", "project_root = os.path.abspath('..')\n", "sys.path.append(project_root)\n", "\n", "print(f\"项目根目录: {project_root}\")\n", "print(f\"PyTorch版本: {torch.__version__}\")\n", "\n", "# 设置设备\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f\"使用设备: {device}\")\n", "\n", "# 设置随机种子以确保结果可重现\n", "torch.manual_seed(42)\n", "np.random.seed(42)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 注意力机制详解\n", "\n", "注意力机制是Transformer的核心。它的基本思想是：给定查询(Query)、键(Key)和值(Value)，计算查询与所有键的相似度，然后用这些相似度作为权重对值进行加权求和。\n", "\n", "### 缩放点积注意力(Scaled Dot-Product Attention)\n", "\n", "公式：$\\text{Attention}(Q, K, V) = \\text{softmax}(\\frac{QK^T}{\\sqrt{d_k}})V$\n", "\n", "其中：\n", "- $Q$：查询矩阵 (seq_len, d_k)\n", "- $K$：键矩阵 (seq_len, d_k)\n", "- $V$：值矩阵 (seq_len, d_v)\n", "- $d_k$：键的维度，用于缩放"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class ScaledDotProductAttention(nn.Module):\n", "    \"\"\"\n", "    缩放点积注意力机制\n", "    \n", "    这是Transformer中最基础的注意力机制\n", "    \"\"\"\n", "    \n", "    def __init__(self, dropout=0.1):\n", "        super(ScaledDotProductAttention, self).__init__()\n", "        self.dropout = nn.Dropout(dropout)\n", "    \n", "    def forward(self, query, key, value, mask=None):\n", "        \"\"\"\n", "        前向传播\n", "        \n", "        Args:\n", "            query (torch.Tensor): 查询 (batch_size, seq_len, d_k)\n", "            key (torch.Tensor): 键 (batch_size, seq_len, d_k)\n", "            value (torch.Tensor): 值 (batch_size, seq_len, d_v)\n", "            mask (torch.Tensor): 掩码 (batch_size, seq_len, seq_len)\n", "        \n", "        Returns:\n", "            tuple: (output, attention_weights)\n", "        \"\"\"\n", "        d_k = query.size(-1)\n", "        \n", "        # 计算注意力分数：Q * K^T / sqrt(d_k)\n", "        scores = torch.matmul(query, key.transpose(-2, -1)) / math.sqrt(d_k)\n", "        \n", "        # 应用掩码（如果提供）\n", "        if mask is not None:\n", "            scores = scores.masked_fill(mask == 0, -1e9)\n", "        \n", "        # 计算注意力权重\n", "        attention_weights = F.softmax(scores, dim=-1)\n", "        attention_weights = self.dropout(attention_weights)\n", "        \n", "        # 应用注意力权重到值\n", "        output = torch.matmul(attention_weights, value)\n", "        \n", "        return output, attention_weights\n", "\n", "def demonstrate_attention():\n", "    \"\"\"\n", "    演示注意力机制的工作原理\n", "    \"\"\"\n", "    print(\"=== 注意力机制演示 ===\")\n", "    \n", "    # 创建示例数据\n", "    batch_size, seq_len, d_model = 1, 5, 8\n", "    \n", "    # 随机初始化Q, K, V\n", "    query = torch.randn(batch_size, seq_len, d_model)\n", "    key = torch.randn(batch_size, seq_len, d_model)\n", "    value = torch.randn(batch_size, seq_len, d_model)\n", "    \n", "    print(f\"输入维度:\")\n", "    print(f\"  Query: {query.shape}\")\n", "    print(f\"  Key: {key.shape}\")\n", "    print(f\"  Value: {value.shape}\")\n", "    \n", "    # 创建注意力层\n", "    attention = ScaledDotProductAttention()\n", "    \n", "    # 前向传播\n", "    output, weights = attention(query, key, value)\n", "    \n", "    print(f\"\\n输出维度:\")\n", "    print(f\"  Output: {output.shape}\")\n", "    print(f\"  Attention weights: {weights.shape}\")\n", "    \n", "    # 可视化注意力权重\n", "    plt.figure(figsize=(8, 6))\n", "    sns.heatmap(weights[0].detach().numpy(), \n", "                annot=True, fmt='.3f', cmap='Blues',\n", "                xticklabels=[f'K{i}' for i in range(seq_len)],\n", "                yticklabels=[f'Q{i}' for i in range(seq_len)])\n", "    plt.title('注意力权重矩阵')\n", "    plt.xlabel('Key位置')\n", "    plt.ylabel('Query位置')\n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # 验证注意力权重的性质\n", "    print(f\"\\n注意力权重性质验证:\")\n", "    row_sums = weights.sum(dim=-1)\n", "    print(f\"  每行权重和: {row_sums[0].detach().numpy()}\")\n", "    print(f\"  是否接近1: {torch.allclose(row_sums, torch.ones_like(row_sums))}\")\n", "    \n", "    return output, weights\n", "\n", "# 运行演示\n", "demo_output, demo_weights = demonstrate_attention()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 多头注意力(Multi-Head Attention)\n", "\n", "多头注意力是Transformer的核心组件之一。它的思想是：\n", "\n", "1. 将Q、K、V分别投影到多个不同的子空间\n", "2. 在每个子空间中并行计算注意力\n", "3. 将所有头的输出拼接起来\n", "4. 通过线性变换得到最终输出\n", "\n", "公式：$\\text{MultiHead}(Q, K, V) = \\text{Concat}(\\text{head}_1, ..., \\text{head}_h)W^O$\n", "\n", "其中：$\\text{head}_i = \\text{Attention}(QW_i^Q, KW_i^K, VW_i^V)$"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class MultiHeadAttention(nn.Module):\n", "    \"\"\"\n", "    多头注意力机制\n", "    \n", "    允许模型同时关注来自不同表示子空间的信息\n", "    \"\"\"\n", "    \n", "    def __init__(self, d_model, num_heads, dropout=0.1):\n", "        super(<PERSON>H<PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        assert d_model % num_heads == 0\n", "        \n", "        self.d_model = d_model\n", "        self.num_heads = num_heads\n", "        self.d_k = d_model // num_heads\n", "        \n", "        # 线性变换层\n", "        self.w_q = nn.Linear(d_model, d_model)\n", "        self.w_k = nn.Linear(d_model, d_model)\n", "        self.w_v = nn.Linear(d_model, d_model)\n", "        self.w_o = nn.Linear(d_model, d_model)\n", "        \n", "        # 注意力层\n", "        self.attention = ScaledDotProductAttention(dropout)\n", "        \n", "        self.dropout = nn.Dropout(dropout)\n", "    \n", "    def forward(self, query, key, value, mask=None):\n", "        \"\"\"\n", "        前向传播\n", "        \n", "        Args:\n", "            query (torch.Tensor): 查询 (batch_size, seq_len, d_model)\n", "            key (torch.Tensor): 键 (batch_size, seq_len, d_model)\n", "            value (torch.Tensor): 值 (batch_size, seq_len, d_model)\n", "            mask (torch.Tensor): 掩码\n", "        \n", "        Returns:\n", "            tuple: (output, attention_weights)\n", "        \"\"\"\n", "        batch_size, seq_len = query.size(0), query.size(1)\n", "        \n", "        # 1. 线性变换并重塑为多头格式\n", "        Q = self.w_q(query).view(batch_size, seq_len, self.num_heads, self.d_k).transpose(1, 2)\n", "        K = self.w_k(key).view(batch_size, seq_len, self.num_heads, self.d_k).transpose(1, 2)\n", "        V = self.w_v(value).view(batch_size, seq_len, self.num_heads, self.d_k).transpose(1, 2)\n", "        \n", "        # 调整掩码维度\n", "        if mask is not None:\n", "            mask = mask.unsqueeze(1).repeat(1, self.num_heads, 1, 1)\n", "        \n", "        # 2. 应用注意力机制\n", "        attn_output, attn_weights = self.attention(Q, K, V, mask)\n", "        \n", "        # 3. 拼接多头输出\n", "        attn_output = attn_output.transpose(1, 2).contiguous().view(\n", "            batch_size, seq_len, self.d_model\n", "        )\n", "        \n", "        # 4. 最终线性变换\n", "        output = self.w_o(attn_output)\n", "        \n", "        return output, attn_weights\n", "\n", "def demonstrate_multihead_attention():\n", "    \"\"\"\n", "    演示多头注意力机制\n", "    \"\"\"\n", "    print(\"\\n=== 多头注意力演示 ===\")\n", "    \n", "    # 参数设置\n", "    batch_size, seq_len, d_model = 2, 6, 512\n", "    num_heads = 8\n", "    \n", "    # 创建输入数据\n", "    x = torch.randn(batch_size, seq_len, d_model)\n", "    \n", "    print(f\"输入维度: {x.shape}\")\n", "    print(f\"模型维度: {d_model}\")\n", "    print(f\"注意力头数: {num_heads}\")\n", "    print(f\"每头维度: {d_model // num_heads}\")\n", "    \n", "    # 创建多头注意力层\n", "    mha = MultiHeadAttention(d_model, num_heads)\n", "    \n", "    # 前向传播（自注意力：Q=K=V=x）\n", "    output, weights = mha(x, x, x)\n", "    \n", "    print(f\"\\n输出维度: {output.shape}\")\n", "    print(f\"注意力权重维度: {weights.shape}\")\n", "    \n", "    # 可视化第一个样本的第一个头的注意力权重\n", "    plt.figure(figsize=(10, 8))\n", "    \n", "    # 显示前4个头的注意力权重\n", "    for i in range(min(4, num_heads)):\n", "        plt.subplot(2, 2, i + 1)\n", "        sns.heatmap(weights[0, i].detach().numpy(), \n", "                   annot=True, fmt='.2f', cmap='Blues')\n", "        plt.title(f'注意力头 {i+1}')\n", "        plt.xlabel('Key位置')\n", "        plt.ylabel('Query位置')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    return output, weights\n", "\n", "# 运行多头注意力演示\n", "mha_output, mha_weights = demonstrate_multihead_attention()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 位置编码(Positional Encoding)\n", "\n", "由于Transformer没有循环或卷积结构，它无法感知序列中元素的位置信息。位置编码的作用是为模型提供位置信息。\n", "\n", "### 正弦位置编码\n", "\n", "原始Transformer使用正弦和余弦函数来生成位置编码：\n", "\n", "- $PE_{(pos, 2i)} = \\sin(pos / 10000^{2i/d_{model}})$\n", "- $PE_{(pos, 2i+1)} = \\cos(pos / 10000^{2i/d_{model}})$\n", "\n", "其中：\n", "- $pos$：位置\n", "- $i$：维度索引\n", "- $d_{model}$：模型维度"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class PositionalEncoding(nn.Module):\n", "    \"\"\"\n", "    位置编码\n", "    \n", "    为序列中的每个位置添加位置信息\n", "    \"\"\"\n", "    \n", "    def __init__(self, d_model, max_len=5000, dropout=0.1):\n", "        super(<PERSON>si<PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        self.dropout = nn.Dropout(dropout)\n", "        \n", "        # 创建位置编码矩阵\n", "        pe = torch.zeros(max_len, d_model)\n", "        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)\n", "        \n", "        # 计算除数项\n", "        div_term = torch.exp(torch.arange(0, d_model, 2).float() * \n", "                           (-math.log(10000.0) / d_model))\n", "        \n", "        # 应用正弦和余弦函数\n", "        pe[:, 0::2] = torch.sin(position * div_term)  # 偶数位置\n", "        pe[:, 1::2] = torch.cos(position * div_term)  # 奇数位置\n", "        \n", "        pe = pe.unsqueeze(0).transpose(0, 1)  # (max_len, 1, d_model)\n", "        \n", "        # 注册为buffer，不参与梯度更新\n", "        self.register_buffer('pe', pe)\n", "    \n", "    def forward(self, x):\n", "        \"\"\"\n", "        前向传播\n", "        \n", "        Args:\n", "            x (torch.Tensor): 输入序列 (seq_len, batch_size, d_model)\n", "        \n", "        Returns:\n", "            torch.Tensor: 添加位置编码后的序列\n", "        \"\"\"\n", "        x = x + self.pe[:x.size(0), :]\n", "        return self.dropout(x)\n", "\n", "def visualize_positional_encoding():\n", "    \"\"\"\n", "    可视化位置编码\n", "    \"\"\"\n", "    print(\"\\n=== 位置编码可视化 ===\")\n", "    \n", "    d_model = 128\n", "    max_len = 100\n", "    \n", "    # 创建位置编码\n", "    pe = PositionalEncoding(d_model, max_len)\n", "    \n", "    # 获取位置编码矩阵\n", "    pos_encoding = pe.pe[:max_len, 0, :].numpy()  # (max_len, d_model)\n", "    \n", "    print(f\"位置编码矩阵形状: {pos_encoding.shape}\")\n", "    \n", "    # 可视化\n", "    fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "    \n", "    # 1. 完整的位置编码矩阵\n", "    im1 = axes[0, 0].imshow(pos_encoding.T, cmap='RdBu', aspect='auto')\n", "    axes[0, 0].set_title('位置编码矩阵')\n", "    axes[0, 0].set_xlabel('位置')\n", "    axes[0, 0].set_ylabel('维度')\n", "    plt.colorbar(im1, ax=axes[0, 0])\n", "    \n", "    # 2. 前几个位置的编码\n", "    positions_to_show = [0, 1, 2, 3, 4]\n", "    for i, pos in enumerate(positions_to_show):\n", "        axes[0, 1].plot(pos_encoding[pos, :20], label=f'位置 {pos}')\n", "    axes[0, 1].set_title('不同位置的编码模式（前20维）')\n", "    axes[0, 1].set_xlabel('维度')\n", "    axes[0, 1].set_ylabel('编码值')\n", "    axes[0, 1].legend()\n", "    axes[0, 1].grid(True)\n", "    \n", "    # 3. 特定维度随位置的变化\n", "    dims_to_show = [0, 1, 10, 11]\n", "    for dim in dims_to_show:\n", "        axes[1, 0].plot(pos_encoding[:50, dim], label=f'维度 {dim}')\n", "    axes[1, 0].set_title('特定维度随位置的变化')\n", "    axes[1, 0].set_xlabel('位置')\n", "    axes[1, 0].set_ylabel('编码值')\n", "    axes[1, 0].legend()\n", "    axes[1, 0].grid(True)\n", "    \n", "    # 4. 位置编码的频率特性\n", "    freqs = []\n", "    for i in range(0, d_model, 2):\n", "        freq = 1.0 / (10000 ** (i / d_model))\n", "        freqs.append(freq)\n", "    \n", "    axes[1, 1].semilogy(freqs)\n", "    axes[1, 1].set_title('位置编码的频率分布')\n", "    axes[1, 1].set_xlabel('维度对索引')\n", "    axes[1, 1].set_ylabel('频率 (log scale)')\n", "    axes[1, 1].grid(True)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    return pe\n", "\n", "# 可视化位置编码\n", "pos_encoder = visualize_positional_encoding()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Transformer编码器层\n", "\n", "Transformer编码器层包含两个主要组件：\n", "1. 多头自注意力机制\n", "2. 位置前馈网络(Position-wise Feed-Forward Network)\n", "\n", "每个组件都使用残差连接和层归一化。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class PositionwiseFeedForward(nn.Module):\n", "    \"\"\"\n", "    位置前馈网络\n", "    \n", "    两层线性变换，中间使用ReLU激活函数\n", "    FFN(x) = max(0, xW1 + b1)W2 + b2\n", "    \"\"\"\n", "    \n", "    def __init__(self, d_model, d_ff, dropout=0.1):\n", "        super(PositionwiseF<PERSON>For<PERSON>, self).__init__()\n", "        self.w_1 = nn.Linear(d_model, d_ff)\n", "        self.w_2 = nn.Linear(d_ff, d_model)\n", "        self.dropout = nn.Dropout(dropout)\n", "    \n", "    def forward(self, x):\n", "        return self.w_2(self.dropout(<PERSON><PERSON>relu(self.w_1(x))))\n", "\n", "class LayerNorm(nn.Module):\n", "    \"\"\"\n", "    层归一化\n", "    \n", "    对每个样本的特征维度进行归一化\n", "    \"\"\"\n", "    \n", "    def __init__(self, features, eps=1e-6):\n", "        super(<PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        self.a_2 = nn.Parameter(torch.ones(features))\n", "        self.b_2 = nn.Parameter(torch.zeros(features))\n", "        self.eps = eps\n", "    \n", "    def forward(self, x):\n", "        mean = x.mean(-1, keepdim=True)\n", "        std = x.std(-1, keepdim=True)\n", "        return self.a_2 * (x - mean) / (std + self.eps) + self.b_2\n", "\n", "class TransformerEncoder<PERSON>ayer(nn.Module):\n", "    \"\"\"\n", "    Transformer编码器层\n", "    \n", "    包含多头自注意力和前馈网络，都使用残差连接和层归一化\n", "    \"\"\"\n", "    \n", "    def __init__(self, d_model, num_heads, d_ff, dropout=0.1):\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        self.self_attn = MultiHeadAttention(d_model, num_heads, dropout)\n", "        self.feed_forward = PositionwiseFeedForward(d_model, d_ff, dropout)\n", "        self.norm1 = LayerNorm(d_model)\n", "        self.norm2 = LayerNorm(d_model)\n", "        self.dropout = nn.Dropout(dropout)\n", "    \n", "    def forward(self, x, mask=None):\n", "        \"\"\"\n", "        前向传播\n", "        \n", "        Args:\n", "            x (torch.Tensor): 输入 (batch_size, seq_len, d_model)\n", "            mask (torch.Tensor): 注意力掩码\n", "        \n", "        Returns:\n", "            torch.Tensor: 编码后的表示\n", "        \"\"\"\n", "        # 多头自注意力 + 残差连接 + 层归一化\n", "        attn_output, _ = self.self_attn(x, x, x, mask)\n", "        x = self.norm1(x + self.dropout(attn_output))\n", "        \n", "        # 前馈网络 + 残差连接 + 层归一化\n", "        ff_output = self.feed_forward(x)\n", "        x = self.norm2(x + self.dropout(ff_output))\n", "        \n", "        return x\n", "\n", "class TransformerEncoder(nn.Module):\n", "    \"\"\"\n", "    Transformer编码器\n", "    \n", "    由多个编码器层堆叠而成\n", "    \"\"\"\n", "    \n", "    def __init__(self, encoder_layer, num_layers):\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        self.layers = nn.ModuleList([copy.deepcopy(encoder_layer) for _ in range(num_layers)])\n", "        self.norm = LayerNorm(encoder_layer.self_attn.d_model)\n", "    \n", "    def forward(self, x, mask=None):\n", "        \"\"\"\n", "        前向传播\n", "        \n", "        Args:\n", "            x (torch.Tensor): 输入序列\n", "            mask (torch.Tensor): 注意力掩码\n", "        \n", "        Returns:\n", "            torch.Tensor: 编码后的表示\n", "        \"\"\"\n", "        for layer in self.layers:\n", "            x = layer(x, mask)\n", "        return self.norm(x)\n", "\n", "def demonstrate_encoder():\n", "    \"\"\"\n", "    演示Transformer编码器\n", "    \"\"\"\n", "    print(\"\\n=== Transformer编码器演示 ===\")\n", "    \n", "    # 参数设置\n", "    batch_size, seq_len, d_model = 2, 10, 512\n", "    num_heads = 8\n", "    d_ff = 2048\n", "    num_layers = 3\n", "    \n", "    # 创建输入数据\n", "    x = torch.randn(batch_size, seq_len, d_model)\n", "    \n", "    print(f\"输入维度: {x.shape}\")\n", "    print(f\"编码器层数: {num_layers}\")\n", "    \n", "    # 创建编码器\n", "    encoder_layer = TransformerEncoderLayer(d_model, num_heads, d_ff)\n", "    encoder = TransformerEncoder(encoder_layer, num_layers)\n", "    \n", "    # 前向传播\n", "    encoded = encoder(x)\n", "    \n", "    print(f\"编码后维度: {encoded.shape}\")\n", "    print(f\"输入输出维度是否相同: {x.shape == encoded.shape}\")\n", "    \n", "    # 分析编码前后的特征变化\n", "    print(f\"\\n特征变化分析:\")\n", "    print(f\"输入均值: {x.mean().item():.4f}, 标准差: {x.std().item():.4f}\")\n", "    print(f\"输出均值: {encoded.mean().item():.4f}, 标准差: {encoded.std().item():.4f}\")\n", "    \n", "    return encoded\n", "\n", "# 运行编码器演示\n", "encoded_output = demonstrate_encoder()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Transformer解码器层\n", "\n", "解码器层比编码器层多了一个组件：\n", "1. 掩码多头自注意力（防止看到未来信息）\n", "2. 编码器-解码器注意力（关注编码器输出）\n", "3. 位置前馈网络"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def subsequent_mask(size):\n", "    \"\"\"\n", "    创建后续掩码，防止解码器看到未来的信息\n", "    \n", "    Args:\n", "        size (int): 序列长度\n", "    \n", "    Returns:\n", "        torch.Tensor: 下三角掩码矩阵\n", "    \"\"\"\n", "    attn_shape = (1, size, size)\n", "    subsequent_mask = np.triu(np.ones(attn_shape), k=1).astype('uint8')\n", "    return torch.from_numpy(subsequent_mask) == 0\n", "\n", "class TransformerDecoderLayer(nn.Module):\n", "    \"\"\"\n", "    Transformer解码器层\n", "    \n", "    包含三个主要组件：\n", "    1. 掩码多头自注意力\n", "    2. 编码器-解码器注意力\n", "    3. 前馈网络\n", "    \"\"\"\n", "    \n", "    def __init__(self, d_model, num_heads, d_ff, dropout=0.1):\n", "        super(Transformer<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        self.self_attn = MultiHeadAttention(d_model, num_heads, dropout)\n", "        self.enc_attn = MultiHeadAttention(d_model, num_heads, dropout)\n", "        self.feed_forward = PositionwiseFeedForward(d_model, d_ff, dropout)\n", "        self.norm1 = LayerNorm(d_model)\n", "        self.norm2 = LayerNorm(d_model)\n", "        self.norm3 = LayerNorm(d_model)\n", "        self.dropout = nn.Dropout(dropout)\n", "    \n", "    def forward(self, x, encoder_output, src_mask=None, tgt_mask=None):\n", "        \"\"\"\n", "        前向传播\n", "        \n", "        Args:\n", "            x (torch.Tensor): 目标序列 (batch_size, tgt_len, d_model)\n", "            encoder_output (torch.Tensor): 编码器输出 (batch_size, src_len, d_model)\n", "            src_mask (torch.Tensor): 源序列掩码\n", "            tgt_mask (torch.Tensor): 目标序列掩码\n", "        \n", "        Returns:\n", "            torch.Tensor: 解码后的表示\n", "        \"\"\"\n", "        # 1. 掩码多头自注意力\n", "        self_attn_output, _ = self.self_attn(x, x, x, tgt_mask)\n", "        x = self.norm1(x + self.dropout(self_attn_output))\n", "        \n", "        # 2. 编码器-解码器注意力\n", "        enc_attn_output, _ = self.enc_attn(x, encoder_output, encoder_output, src_mask)\n", "        x = self.norm2(x + self.dropout(enc_attn_output))\n", "        \n", "        # 3. 前馈网络\n", "        ff_output = self.feed_forward(x)\n", "        x = self.norm3(x + self.dropout(ff_output))\n", "        \n", "        return x\n", "\n", "class TransformerDecoder(nn.Module):\n", "    \"\"\"\n", "    Transformer解码器\n", "    \n", "    由多个解码器层堆叠而成\n", "    \"\"\"\n", "    \n", "    def __init__(self, decoder_layer, num_layers):\n", "        super(TransformerDecoder, self).__init__()\n", "        self.layers = nn.ModuleList([copy.deepcopy(decoder_layer) for _ in range(num_layers)])\n", "        self.norm = LayerNorm(decoder_layer.self_attn.d_model)\n", "    \n", "    def forward(self, x, encoder_output, src_mask=None, tgt_mask=None):\n", "        \"\"\"\n", "        前向传播\n", "        \n", "        Args:\n", "            x (torch.Tensor): 目标序列\n", "            encoder_output (torch.Tensor): 编码器输出\n", "            src_mask (torch.Tensor): 源序列掩码\n", "            tgt_mask (torch.Tensor): 目标序列掩码\n", "        \n", "        Returns:\n", "            torch.Tensor: 解码后的表示\n", "        \"\"\"\n", "        for layer in self.layers:\n", "            x = layer(x, encoder_output, src_mask, tgt_mask)\n", "        return self.norm(x)\n", "\n", "def demonstrate_decoder():\n", "    \"\"\"\n", "    演示Transformer解码器\n", "    \"\"\"\n", "    print(\"\\n=== Transformer解码器演示 ===\")\n", "    \n", "    # 参数设置\n", "    batch_size = 2\n", "    src_len, tgt_len = 10, 8\n", "    d_model = 512\n", "    num_heads = 8\n", "    d_ff = 2048\n", "    num_layers = 3\n", "    \n", "    # 创建输入数据\n", "    encoder_output = torch.randn(batch_size, src_len, d_model)  # 编码器输出\n", "    tgt_input = torch.randn(batch_size, tgt_len, d_model)      # 目标序列输入\n", "    \n", "    # 创建掩码\n", "    tgt_mask = subsequent_mask(tgt_len)  # 防止看到未来信息\n", "    \n", "    print(f\"编码器输出维度: {encoder_output.shape}\")\n", "    print(f\"目标输入维度: {tgt_input.shape}\")\n", "    print(f\"目标掩码维度: {tgt_mask.shape}\")\n", "    \n", "    # 可视化掩码\n", "    plt.figure(figsize=(8, 6))\n", "    plt.imshow(tgt_mask[0].numpy(), cmap='Blues')\n", "    plt.title('解码器掩码矩阵（防止看到未来信息）')\n", "    plt.xlabel('Key位置')\n", "    plt.ylabel('Query位置')\n", "    plt.colorbar()\n", "    plt.show()\n", "    \n", "    # 创建解码器\n", "    decoder_layer = TransformerDecoderLayer(d_model, num_heads, d_ff)\n", "    decoder = TransformerDecoder(decoder_layer, num_layers)\n", "    \n", "    # 前向传播\n", "    decoded = decoder(tgt_input, encoder_output, tgt_mask=tgt_mask)\n", "    \n", "    print(f\"\\n解码后维度: {decoded.shape}\")\n", "    print(f\"解码器层数: {num_layers}\")\n", "    \n", "    return decoded\n", "\n", "# 运行解码器演示\n", "decoded_output = demonstrate_decoder()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 完整的Transformer模型\n", "\n", "现在我们将所有组件组合成一个完整的Transformer模型："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class SimpleTransformer(nn.Module):\n", "    \"\"\"\n", "    简化版Transformer模型\n", "    \n", "    用于演示Transformer的完整工作流程\n", "    \"\"\"\n", "    \n", "    def __init__(self, src_vocab_size, tgt_vocab_size, d_model=512, \n", "                 num_heads=8, num_layers=6, d_ff=2048, max_len=5000, dropout=0.1):\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        \n", "        self.d_model = d_model\n", "        \n", "        # 词嵌入层\n", "        self.src_embed = nn.Embedding(src_vocab_size, d_model)\n", "        self.tgt_embed = nn.Embedding(tgt_vocab_size, d_model)\n", "        \n", "        # 位置编码\n", "        self.pos_encoding = PositionalEncoding(d_model, max_len, dropout)\n", "        \n", "        # 编码器和解码器\n", "        encoder_layer = TransformerEncoderLayer(d_model, num_heads, d_ff, dropout)\n", "        self.encoder = TransformerEncoder(encoder_layer, num_layers)\n", "        \n", "        decoder_layer = TransformerDecoderLayer(d_model, num_heads, d_ff, dropout)\n", "        self.decoder = TransformerDecoder(decoder_layer, num_layers)\n", "        \n", "        # 输出投影层\n", "        self.output_projection = nn.Linear(d_model, tgt_vocab_size)\n", "        \n", "        # 初始化参数\n", "        self._init_parameters()\n", "    \n", "    def _init_parameters(self):\n", "        \"\"\"初始化模型参数\"\"\"\n", "        for p in self.parameters():\n", "            if p.dim() > 1:\n", "                nn.init.xavier_uniform_(p)\n", "    \n", "    def forward(self, src, tgt, src_mask=None, tgt_mask=None):\n", "        \"\"\"\n", "        前向传播\n", "        \n", "        Args:\n", "            src (torch.Tensor): 源序列 (batch_size, src_len)\n", "            tgt (torch.Tensor): 目标序列 (batch_size, tgt_len)\n", "            src_mask (torch.Tensor): 源序列掩码\n", "            tgt_mask (torch.Tensor): 目标序列掩码\n", "        \n", "        Returns:\n", "            torch.Tensor: 输出概率分布 (batch_size, tgt_len, tgt_vocab_size)\n", "        \"\"\"\n", "        # 编码器\n", "        src_embedded = self.src_embed(src) * math.sqrt(self.d_model)\n", "        src_embedded = self.pos_encoding(src_embedded.transpose(0, 1)).transpose(0, 1)\n", "        encoder_output = self.encoder(src_embedded, src_mask)\n", "        \n", "        # 解码器\n", "        tgt_embedded = self.tgt_embed(tgt) * math.sqrt(self.d_model)\n", "        tgt_embedded = self.pos_encoding(tgt_embedded.transpose(0, 1)).transpose(0, 1)\n", "        decoder_output = self.decoder(tgt_embedded, encoder_output, src_mask, tgt_mask)\n", "        \n", "        # 输出投影\n", "        output = self.output_projection(decoder_output)\n", "        \n", "        return output\n", "\n", "def demonstrate_complete_transformer():\n", "    \"\"\"\n", "    演示完整的Transformer模型\n", "    \"\"\"\n", "    print(\"\\n=== 完整Transformer模型演示 ===\")\n", "    \n", "    # 参数设置\n", "    src_vocab_size = 1000\n", "    tgt_vocab_size = 800\n", "    d_model = 256  # 使用较小的模型以便演示\n", "    num_heads = 8\n", "    num_layers = 3\n", "    d_ff = 1024\n", "    \n", "    batch_size = 2\n", "    src_len = 12\n", "    tgt_len = 10\n", "    \n", "    # 创建模型\n", "    model = SimpleTransformer(\n", "        src_vocab_size, tgt_vocab_size, d_model, \n", "        num_heads, num_layers, d_ff\n", "    )\n", "    \n", "    # 创建输入数据（随机词汇索引）\n", "    src = torch.randint(0, src_vocab_size, (batch_size, src_len))\n", "    tgt = torch.randint(0, tgt_vocab_size, (batch_size, tgt_len))\n", "    \n", "    # 创建掩码\n", "    tgt_mask = subsequent_mask(tgt_len)\n", "    \n", "    print(f\"模型参数:\")\n", "    print(f\"  源词汇表大小: {src_vocab_size}\")\n", "    print(f\"  目标词汇表大小: {tgt_vocab_size}\")\n", "    print(f\"  模型维度: {d_model}\")\n", "    print(f\"  注意力头数: {num_heads}\")\n", "    print(f\"  层数: {num_layers}\")\n", "    \n", "    print(f\"\\n输入数据:\")\n", "    print(f\"  源序列形状: {src.shape}\")\n", "    print(f\"  目标序列形状: {tgt.shape}\")\n", "    \n", "    # 前向传播\n", "    with torch.no_grad():\n", "        output = model(src, tgt, tgt_mask=tgt_mask)\n", "    \n", "    print(f\"\\n输出:\")\n", "    print(f\"  输出形状: {output.shape}\")\n", "    print(f\"  输出含义: (batch_size, tgt_len, tgt_vocab_size)\")\n", "    \n", "    # 计算模型参数数量\n", "    total_params = sum(p.numel() for p in model.parameters())\n", "    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)\n", "    \n", "    print(f\"\\n模型统计:\")\n", "    print(f\"  总参数数量: {total_params:,}\")\n", "    print(f\"  可训练参数数量: {trainable_params:,}\")\n", "    \n", "    # 分析输出分布\n", "    output_probs = F.softmax(output, dim=-1)\n", "    print(f\"\\n输出分析:\")\n", "    print(f\"  输出概率和: {output_probs.sum(dim=-1)[0, 0].item():.4f} (应该接近1.0)\")\n", "    print(f\"  最大概率: {output_probs.max().item():.4f}\")\n", "    print(f\"  最小概率: {output_probs.min().item():.6f}\")\n", "    \n", "    return model, output\n", "\n", "# 运行完整模型演示\n", "transformer_model, transformer_output = demonstrate_complete_transformer()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 小结\n", "\n", "在本节中，我们深入学习了Transformer架构的核心组件：\n", "\n", "### 核心概念\n", "\n", "1. **注意力机制**：\n", "   - **缩放点积注意力**：基础的注意力计算方法\n", "   - **多头注意力**：并行计算多个注意力头，捕获不同类型的依赖关系\n", "   - **自注意力**：序列内部元素之间的注意力\n", "   - **交叉注意力**：编码器-解码器之间的注意力\n", "\n", "2. **位置编码**：\n", "   - 为序列提供位置信息\n", "   - 使用正弦和余弦函数生成\n", "   - 允许模型处理不同长度的序列\n", "\n", "3. **Transformer层**：\n", "   - **编码器层**：自注意力 + 前馈网络\n", "   - **解码器层**：掩码自注意力 + 交叉注意力 + 前馈网络\n", "   - 残差连接和层归一化\n", "\n", "### 关键特性\n", "\n", "1. **并行计算**：\n", "   - 不像RNN需要顺序处理\n", "   - 可以并行计算序列中的所有位置\n", "   - 大大提高了训练效率\n", "\n", "2. **长距离依赖**：\n", "   - 注意力机制直接连接任意两个位置\n", "   - 有效捕获长距离的依赖关系\n", "   - 避免了梯度消失问题\n", "\n", "3. **可解释性**：\n", "   - 注意力权重提供了模型决策的可视化\n", "   - 可以分析模型关注的内容\n", "   - 有助于理解和调试模型\n", "\n", "### 在R2Gen中的应用\n", "\n", "在R2Gen项目中，Transformer用于：\n", "- **编码器**：处理视觉特征，生成上下文表示\n", "- **解码器**：基于视觉特征生成医学报告文本\n", "- **注意力可视化**：显示模型关注的图像区域\n", "\n", "### 下一步\n", "\n", "在下一个notebook中，我们将学习R2Gen的核心创新：**记忆驱动机制**。这是R2Gen相比标准Transformer的主要改进，能够增强模型的记忆能力和生成质量。\n", "\n", "### 关键收获\n", "\n", "- Transformer通过注意力机制实现序列到序列的转换\n", "- 多头注意力允许模型关注不同类型的信息\n", "- 位置编码为模型提供序列位置信息\n", "- 残差连接和层归一化稳定训练过程\n", "- Transformer的并行性和长距离依赖能力使其成为NLP的主流架构"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}