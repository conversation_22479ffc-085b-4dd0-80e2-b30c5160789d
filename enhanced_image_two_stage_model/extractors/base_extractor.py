"""
基础特征提取器抽象类

定义统一的特征提取器接口，确保所有提取器输出相同格式的特征。
这是整个Pipeline的核心接口，所有具体的提取器都必须继承此类。
"""

import torch
import torch.nn as nn
from abc import ABC, abstractmethod
from typing import Dict, Tuple, Any, Union, List
from PIL import Image


class BaseFeatureExtractor(ABC):
    """
    抽象特征提取器基类
    
    定义统一的接口，确保所有提取器输出相同格式的特征。
    这是实现可插拔特征提取器Pipeline的关键。
    
    所有继承的提取器必须实现：
    1. extract_features: 提取图像特征
    2. get_feature_dim: 返回特征维度信息
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化特征提取器
        
        Args:
            config: 提取器配置字典，包含模型参数、路径等信息
        """
        self.config = config
        self.extractor_name = config.get('name', 'unknown')
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
    @abstractmethod
    def extract_features(self, images: Union[torch.Tensor, List], mode: str = 'train') -> Dict[str, torch.Tensor]:
        """
        提取图像特征（抽象方法）
        
        Args:
            images: 输入图像
                - 如果是torch.Tensor: (batch_size, 2, 3, 224, 224) for IU X-ray双图像
                - 如果是List: 包含PIL图像的列表
            mode: 模式 ('train' 或 'val')，决定是否使用数据增强
            
        Returns:
            Dict[str, torch.Tensor]: 标准化特征字典
                - 'att_feats': 注意力特征 (batch_size, 98, 2048)
                - 'fc_feats': 全连接特征 (batch_size, 4096)
                - 'metadata': 提取器元信息
                
        注意：
            - 所有提取器必须输出相同的特征格式
            - IU X-ray数据集每个样本包含两张图像，需要拼接处理
            - 特征维度必须与R2Gen原始格式保持一致
        """
        pass
    
    @abstractmethod
    def get_feature_dim(self) -> Dict[str, Tuple]:
        """
        返回特征维度信息（抽象方法）
        
        Returns:
            Dict[str, Tuple]: 特征维度字典
                - 'att_feats': 注意力特征维度，如 (98, 2048)
                - 'fc_feats': 全连接特征维度，如 (4096,)
                - 'visual_backbone': 使用的视觉backbone名称
        """
        pass
    
    def to(self, device: torch.device):
        """
        将提取器移动到指定设备
        
        Args:
            device: 目标设备
        """
        self.device = device
        if hasattr(self, 'model'):
            self.model.to(device)
        return self
    
    def eval(self):
        """设置为评估模式"""
        if hasattr(self, 'model'):
            self.model.eval()
        return self
    
    def train(self):
        """设置为训练模式"""
        if hasattr(self, 'model'):
            self.model.train()
        return self
    
    def _validate_output_format(self, features: Dict[str, torch.Tensor]) -> bool:
        """
        验证输出特征格式是否符合标准
        
        Args:
            features: 提取的特征字典
            
        Returns:
            bool: 格式是否正确
        """
        required_keys = ['att_feats', 'fc_feats', 'metadata']
        
        # 检查必需的键
        for key in required_keys:
            if key not in features:
                raise ValueError(f"Missing required key: {key}")
        
        # 检查特征维度
        att_feats = features['att_feats']
        fc_feats = features['fc_feats']
        
        if len(att_feats.shape) != 3:
            raise ValueError(f"att_feats should have 3 dimensions, got {len(att_feats.shape)}")
        
        if len(fc_feats.shape) != 2:
            raise ValueError(f"fc_feats should have 2 dimensions, got {len(fc_feats.shape)}")
        
        # 检查IU X-ray的标准维度
        batch_size = att_feats.shape[0]
        if att_feats.shape != (batch_size, 98, 2048):
            raise ValueError(f"att_feats shape should be ({batch_size}, 98, 2048), got {att_feats.shape}")
        
        if fc_feats.shape != (batch_size, 4096):
            raise ValueError(f"fc_feats shape should be ({batch_size}, 4096), got {fc_feats.shape}")
        
        return True
    
    def __str__(self) -> str:
        """返回提取器的字符串表示"""
        return f"{self.__class__.__name__}(name={self.extractor_name})"
    
    def __repr__(self) -> str:
        """返回提取器的详细表示"""
        return f"{self.__class__.__name__}(name={self.extractor_name}, config={self.config})"
