{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 03 - 数据预处理与加载\n", "\n", "在这个notebook中，我们将学习如何对医学影像和报告文本进行预处理，并实现高效的数据加载机制。这是深度学习项目中的关键步骤。\n", "\n", "## 学习目标\n", "\n", "- 理解图像预处理的必要性和方法\n", "- 学习文本tokenization和词汇表构建\n", "- 实现数据集类和数据加载器\n", "- 了解批处理和数据增强技术\n", "- 掌握PyTorch数据处理流水线\n", "\n", "## 数据预处理概述\n", "\n", "数据预处理包括两个主要部分：\n", "\n", "1. **图像预处理**：\n", "   - 尺寸标准化\n", "   - 像素值归一化\n", "   - 数据增强（训练时）\n", "   - 格式转换\n", "\n", "2. **文本预处理**：\n", "   - 文本清理\n", "   - 分词（tokenization）\n", "   - 词汇表构建\n", "   - 序列编码"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 导入必要的库\n", "import os\n", "import sys\n", "import json\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import torch\n", "import torch.nn as nn\n", "from torch.utils.data import Dataset, DataLoader\n", "from torchvision import transforms\n", "from PIL import Image\n", "import re\n", "from collections import Counter\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# 设置项目路径\n", "project_root = os.path.abspath('..')\n", "sys.path.append(project_root)\n", "\n", "print(f\"项目根目录: {project_root}\")\n", "print(f\"PyTorch版本: {torch.__version__}\")\n", "print(f\"CUDA可用: {torch.cuda.is_available()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 图像预处理\n", "\n", "首先，我们来学习如何对医学影像进行预处理。医学影像预处理的目标是：\n", "- 统一图像尺寸\n", "- 标准化像素值\n", "- 增强数据多样性（训练时）"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_image_transforms(is_training=True, image_size=224):\n", "    \"\"\"\n", "    创建图像预处理变换\n", "    \n", "    Args:\n", "        is_training (bool): 是否为训练模式\n", "        image_size (int): 目标图像尺寸\n", "    \n", "    Returns:\n", "        transforms.Compose: 图像变换组合\n", "    \"\"\"\n", "    if is_training:\n", "        # 训练时的数据增强\n", "        transform = transforms.Compose([\n", "            transforms.<PERSON><PERSON><PERSON>(256),  # 先放大到256\n", "            transforms.RandomCrop(image_size),  # 随机裁剪到目标尺寸\n", "            transforms.RandomHorizontalFlip(p=0.5),  # 随机水平翻转\n", "            transforms.To<PERSON><PERSON><PERSON>(),  # 转换为张量\n", "            transforms.Normalize(\n", "                mean=[0.485, 0.456, 0.406],  # ImageNet预训练模型的标准化参数\n", "                std=[0.229, 0.224, 0.225]\n", "            )\n", "        ])\n", "    else:\n", "        # 验证/测试时的标准化处理\n", "        transform = transforms.Compose([\n", "            transforms.Resize((image_size, image_size)),  # 直接缩放到目标尺寸\n", "            transforms.To<PERSON><PERSON><PERSON>(),\n", "            transforms.Normalize(\n", "                mean=[0.485, 0.456, 0.406],\n", "                std=[0.229, 0.224, 0.225]\n", "            )\n", "        ])\n", "    \n", "    return transform\n", "\n", "# 创建训练和验证的图像变换\n", "train_transform = create_image_transforms(is_training=True)\n", "val_transform = create_image_transforms(is_training=False)\n", "\n", "print(\"训练时图像变换:\")\n", "print(train_transform)\n", "print(\"\\n验证时图像变换:\")\n", "print(val_transform)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 图像预处理效果演示\n", "\n", "让我们可视化图像预处理的效果："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def visualize_image_preprocessing(image_path, transform, title=\"图像预处理效果\"):\n", "    \"\"\"\n", "    可视化图像预处理效果\n", "    \n", "    Args:\n", "        image_path (str): 图像路径\n", "        transform: 图像变换\n", "        title (str): 图表标题\n", "    \"\"\"\n", "    try:\n", "        # 加载原始图像\n", "        original_img = Image.open(image_path).convert('RGB')\n", "        \n", "        # 应用变换\n", "        transformed_tensor = transform(original_img)\n", "        \n", "        # 反标准化以便可视化\n", "        mean = torch.tensor([0.485, 0.456, 0.406]).view(3, 1, 1)\n", "        std = torch.tensor([0.229, 0.224, 0.225]).view(3, 1, 1)\n", "        transformed_img = transformed_tensor * std + mean\n", "        transformed_img = torch.clamp(transformed_img, 0, 1)\n", "        \n", "        # 转换为numpy数组用于显示\n", "        transformed_np = transformed_img.permute(1, 2, 0).numpy()\n", "        \n", "        # 可视化\n", "        fig, axes = plt.subplots(1, 2, figsize=(12, 5))\n", "        \n", "        # 原始图像\n", "        axes[0].imshow(original_img, cmap='gray')\n", "        axes[0].set_title(f\"原始图像 {original_img.size}\")\n", "        axes[0].axis('off')\n", "        \n", "        # 预处理后图像\n", "        axes[1].imshow(transformed_np)\n", "        axes[1].set_title(f\"预处理后 {transformed_tensor.shape[1:]}\")\n", "        axes[1].axis('off')\n", "        \n", "        plt.suptitle(title)\n", "        plt.tight_layout()\n", "        plt.show()\n", "        \n", "        # 打印张量信息\n", "        print(f\"原始图像尺寸: {original_img.size}\")\n", "        print(f\"预处理后张量形状: {transformed_tensor.shape}\")\n", "        print(f\"像素值范围: [{transformed_tensor.min():.3f}, {transformed_tensor.max():.3f}]\")\n", "        \n", "    except Exception as e:\n", "        print(f\"处理图像时出错: {e}\")\n", "\n", "# 尝试加载一张示例图像进行演示\n", "annotation_path = os.path.join(project_root, 'data', 'iu_xray', 'annotation.json')\n", "image_dir = os.path.join(project_root, 'data', 'iu_xray', 'images')\n", "\n", "try:\n", "    with open(annotation_path, 'r') as f:\n", "        data = json.load(f)\n", "    \n", "    # 找到第一个存在的图像文件\n", "    sample_image_path = None\n", "    for sample in data['train'][:5]:  # 检查前5个样本\n", "        for img_path in sample['image_path']:\n", "            full_path = os.path.join(image_dir, img_path)\n", "            if os.path.exists(full_path):\n", "                sample_image_path = full_path\n", "                break\n", "        if sample_image_path:\n", "            break\n", "    \n", "    if sample_image_path:\n", "        print(f\"使用示例图像: {sample_image_path}\")\n", "        visualize_image_preprocessing(sample_image_path, val_transform, \"验证时图像预处理\")\n", "    else:\n", "        print(\"未找到可用的示例图像\")\n", "        \n", "except FileNotFoundError:\n", "    print(\"数据集文件未找到，跳过图像预处理演示\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 文本预处理与Tokenization\n", "\n", "现在我们来学习文本预处理，这是自然语言处理的核心步骤。我们需要：\n", "1. 清理文本\n", "2. 分词\n", "3. 构建词汇表\n", "4. 将文本转换为数字序列"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class MedicalReportTokenizer:\n", "    \"\"\"\n", "    医学报告分词器\n", "    \n", "    功能：\n", "    1. 文本清理\n", "    2. 分词\n", "    3. 词汇表构建\n", "    4. 编码/解码\n", "    \"\"\"\n", "    \n", "    def __init__(self, threshold=3):\n", "        \"\"\"\n", "        初始化分词器\n", "        \n", "        Args:\n", "            threshold (int): 词汇频率阈值，低于此值的词汇将被标记为<unk>\n", "        \"\"\"\n", "        self.threshold = threshold\n", "        self.token2idx = {}  # 词汇到索引的映射\n", "        self.idx2token = {}  # 索引到词汇的映射\n", "        \n", "        # 特殊标记\n", "        self.special_tokens = {\n", "            '<pad>': 0,  # 填充标记\n", "            '<unk>': 1,  # 未知词汇标记\n", "            '<start>': 2,  # 序列开始标记\n", "            '<end>': 3   # 序列结束标记\n", "        }\n", "    \n", "    def clean_report(self, report):\n", "        \"\"\"\n", "        清理报告文本\n", "        \n", "        Args:\n", "            report (str): 原始报告文本\n", "        \n", "        Returns:\n", "            str: 清理后的文本\n", "        \"\"\"\n", "        # 转换为小写\n", "        report = report.lower()\n", "        \n", "        # 移除多余的空白字符\n", "        report = re.sub(r'\\s+', ' ', report)\n", "        \n", "        # 处理标点符号（保留句号，移除其他标点）\n", "        report = re.sub(r'[.,?;*!%^&_+():-\\[\\]{}]', '', report)\n", "        report = re.sub(r'[\"\\'/\\\\]', '', report)\n", "        \n", "        # 分割句子并清理\n", "        sentences = report.split('.')\n", "        cleaned_sentences = []\n", "        \n", "        for sentence in sentences:\n", "            sentence = sentence.strip()\n", "            if sentence:  # 非空句子\n", "                cleaned_sentences.append(sentence)\n", "        \n", "        # 重新组合，用句号分隔\n", "        cleaned_report = ' . '.join(cleaned_sentences)\n", "        if cleaned_report and not cleaned_report.endswith(' .'):\n", "            cleaned_report += ' .'\n", "        \n", "        return cleaned_report.strip()\n", "    \n", "    def build_vocabulary(self, reports):\n", "        \"\"\"\n", "        从报告列表构建词汇表\n", "        \n", "        Args:\n", "            reports (list): 报告文本列表\n", "        \"\"\"\n", "        print(\"构建词汇表...\")\n", "        \n", "        # 收集所有词汇\n", "        all_tokens = []\n", "        for report in reports:\n", "            cleaned_report = self.clean_report(report)\n", "            tokens = cleaned_report.split()\n", "            all_tokens.extend(tokens)\n", "        \n", "        # 统计词频\n", "        token_counts = Counter(all_tokens)\n", "        print(f\"总词汇数: {len(all_tokens)}\")\n", "        print(f\"唯一词汇数: {len(token_counts)}\")\n", "        \n", "        # 过滤低频词汇\n", "        vocab = [token for token, count in token_counts.items() \n", "                if count >= self.threshold]\n", "        vocab.sort()  # 排序以确保一致性\n", "        \n", "        print(f\"过滤后词汇数: {len(vocab)} (阈值: {self.threshold})\")\n", "        \n", "        # 构建映射\n", "        self.token2idx = self.special_tokens.copy()\n", "        self.idx2token = {idx: token for token, idx in self.special_tokens.items()}\n", "        \n", "        # 添加词汇表中的词汇\n", "        for token in vocab:\n", "            if token not in self.token2idx:\n", "                idx = len(self.token2idx)\n", "                self.token2idx[token] = idx\n", "                self.idx2token[idx] = token\n", "        \n", "        print(f\"最终词汇表大小: {len(self.token2idx)}\")\n", "    \n", "    def encode(self, report):\n", "        \"\"\"\n", "        将报告文本编码为数字序列\n", "        \n", "        Args:\n", "            report (str): 报告文本\n", "        \n", "        Returns:\n", "            list: 数字序列\n", "        \"\"\"\n", "        cleaned_report = self.clean_report(report)\n", "        tokens = cleaned_report.split()\n", "        \n", "        # 添加开始和结束标记\n", "        sequence = [self.token2idx['<start>']]\n", "        \n", "        for token in tokens:\n", "            if token in self.token2idx:\n", "                sequence.append(self.token2idx[token])\n", "            else:\n", "                sequence.append(self.token2idx['<unk>'])\n", "        \n", "        sequence.append(self.token2idx['<end>'])\n", "        return sequence\n", "    \n", "    def decode(self, sequence):\n", "        \"\"\"\n", "        将数字序列解码为文本\n", "        \n", "        Args:\n", "            sequence (list): 数字序列\n", "        \n", "        Returns:\n", "            str: 解码后的文本\n", "        \"\"\"\n", "        tokens = []\n", "        for idx in sequence:\n", "            if idx in self.idx2token:\n", "                token = self.idx2token[idx]\n", "                if token not in ['<start>', '<end>', '<pad>']:\n", "                    tokens.append(token)\n", "            else:\n", "                break  # 遇到无效索引时停止\n", "        \n", "        return ' '.join(tokens)\n", "    \n", "    def get_vocab_size(self):\n", "        \"\"\"获取词汇表大小\"\"\"\n", "        return len(self.token2idx)\n", "\n", "# 演示分词器的使用\n", "print(\"创建医学报告分词器...\")\n", "tokenizer = MedicalReportTokenizer(threshold=3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 分词器演示\n", "\n", "让我们用实际的医学报告来演示分词器的功能："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 使用数据集中的报告来构建词汇表和演示\n", "try:\n", "    # 加载数据\n", "    with open(annotation_path, 'r') as f:\n", "        data = json.load(f)\n", "    \n", "    # 收集训练集的报告用于构建词汇表\n", "    train_reports = [sample['report'] for sample in data['train']]\n", "    \n", "    # 构建词汇表\n", "    tokenizer.build_vocabulary(train_reports)\n", "    \n", "    # 演示文本处理\n", "    sample_report = train_reports[0]\n", "    print(f\"\\n原始报告:\")\n", "    print(f\"\\\"{sample_report}\\\"\")\n", "    \n", "    # 清理文本\n", "    cleaned = tokenizer.clean_report(sample_report)\n", "    print(f\"\\n清理后:\")\n", "    print(f\"\\\"{cleaned}\\\"\")\n", "    \n", "    # 编码\n", "    encoded = tokenizer.encode(sample_report)\n", "    print(f\"\\n编码后 (前20个数字):\")\n", "    print(encoded[:20])\n", "    \n", "    # 解码\n", "    decoded = tokenizer.decode(encoded)\n", "    print(f\"\\n解码后:\")\n", "    print(f\"\\\"{decoded}\\\"\")\n", "    \n", "    # 显示词汇表统计\n", "    print(f\"\\n词汇表统计:\")\n", "    print(f\"词汇表大小: {tokenizer.get_vocab_size()}\")\n", "    print(f\"特殊标记: {list(tokenizer.special_tokens.keys())}\")\n", "    \n", "    # 显示一些高频词汇\n", "    print(f\"\\n前20个词汇 (按索引):\")\n", "    for i in range(min(20, len(tokenizer.idx2token))):\n", "        print(f\"{i:2d}: {tokenizer.idx2token[i]}\")\n", "        \n", "except FileNotFoundError:\n", "    print(\"数据集文件未找到，使用示例文本演示分词器\")\n", "    \n", "    # 使用示例报告\n", "    example_reports = [\n", "        \"The heart size is normal. The lungs are clear. No pneumothorax or pleural effusion.\",\n", "        \"Cardiac silhouette is within normal limits. No focal consolidation. No pleural effusion.\",\n", "        \"Normal heart size. Clear lungs. No acute findings.\"\n", "    ]\n", "    \n", "    tokenizer.build_vocabulary(example_reports)\n", "    \n", "    for i, report in enumerate(example_reports):\n", "        print(f\"\\n示例 {i+1}:\")\n", "        print(f\"原文: {report}\")\n", "        encoded = tokenizer.encode(report)\n", "        decoded = tokenizer.decode(encoded)\n", "        print(f\"编码: {encoded}\")\n", "        print(f\"解码: {decoded}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 数据集类实现\n", "\n", "现在我们来实现PyTorch的Dataset类，用于加载和预处理数据："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class IUXrayDataset(Dataset):\n", "    \"\"\"\n", "    IU X-Ray数据集类\n", "    \n", "    功能：\n", "    1. 加载图像和报告\n", "    2. 应用预处理变换\n", "    3. 返回批处理友好的数据格式\n", "    \"\"\"\n", "    \n", "    def __init__(self, data_samples, image_dir, tokenizer, transform=None, max_seq_length=60):\n", "        \"\"\"\n", "        初始化数据集\n", "        \n", "        Args:\n", "            data_samples (list): 数据样本列表\n", "            image_dir (str): 图像目录路径\n", "            tokenizer: 分词器实例\n", "            transform: 图像变换\n", "            max_seq_length (int): 最大序列长度\n", "        \"\"\"\n", "        self.data_samples = data_samples\n", "        self.image_dir = image_dir\n", "        self.tokenizer = tokenizer\n", "        self.transform = transform\n", "        self.max_seq_length = max_seq_length\n", "    \n", "    def __len__(self):\n", "        \"\"\"返回数据集大小\"\"\"\n", "        return len(self.data_samples)\n", "    \n", "    def __getitem__(self, idx):\n", "        \"\"\"\n", "        获取单个数据样本\n", "        \n", "        Args:\n", "            idx (int): 样本索引\n", "        \n", "        Returns:\n", "            tuple: (image_id, images, report_ids, report_mask, seq_length)\n", "        \"\"\"\n", "        sample = self.data_samples[idx]\n", "        \n", "        # 获取样本信息\n", "        image_id = sample['id']\n", "        image_paths = sample['image_path']\n", "        report = sample['report']\n", "        \n", "        # 加载和预处理图像\n", "        images = []\n", "        for img_path in image_paths:\n", "            full_path = os.path.join(self.image_dir, img_path)\n", "            try:\n", "                image = Image.open(full_path).convert('RGB')\n", "                if self.transform:\n", "                    image = self.transform(image)\n", "                images.append(image)\n", "            except Exception as e:\n", "                print(f\"加载图像失败 {full_path}: {e}\")\n", "                # 创建一个零图像作为占位符\n", "                if self.transform:\n", "                    dummy_image = torch.zeros(3, 224, 224)\n", "                else:\n", "                    dummy_image = torch.zeros(3, 256, 256)\n", "                images.append(dummy_image)\n", "        \n", "        # 如果只有一张图像，复制一份（IU X-Ray通常有两张图像）\n", "        while len(images) < 2:\n", "            images.append(images[0].clone() if images else torch.zeros(3, 224, 224))\n", "        \n", "        # 堆叠图像\n", "        images = torch.stack(images[:2])  # 只取前两张图像\n", "        \n", "        # 处理报告文本\n", "        report_ids = self.tokenizer.encode(report)\n", "        \n", "        # 截断或填充到最大长度\n", "        if len(report_ids) > self.max_seq_length:\n", "            report_ids = report_ids[:self.max_seq_length]\n", "        \n", "        # 创建掩码（1表示真实词汇，0表示填充）\n", "        seq_length = len(report_ids)\n", "        report_mask = [1] * seq_length\n", "        \n", "        # 填充到最大长度\n", "        pad_length = self.max_seq_length - seq_length\n", "        if pad_length > 0:\n", "            report_ids.extend([self.tokenizer.token2idx['<pad>']] * pad_length)\n", "            report_mask.extend([0] * pad_length)\n", "        \n", "        return (\n", "            image_id,\n", "            images,\n", "            torch.tensor(report_ids, dtype=torch.long),\n", "            torch.tensor(report_mask, dtype=torch.long),\n", "            seq_length\n", "        )\n", "\n", "print(\"IU X-Ray数据集类定义完成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 数据加载器实现\n", "\n", "现在我们创建数据加载器，用于批处理数据："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def collate_fn(batch):\n", "    \"\"\"\n", "    自定义批处理函数\n", "    \n", "    Args:\n", "        batch (list): 批次数据列表\n", "    \n", "    Returns:\n", "        tuple: 批处理后的数据\n", "    \"\"\"\n", "    # 分离各个组件\n", "    image_ids, images, report_ids, report_masks, seq_lengths = zip(*batch)\n", "    \n", "    # 堆叠张量\n", "    images = torch.stack(images, 0)  # (batch_size, 2, 3, 224, 224)\n", "    report_ids = torch.stack(report_ids, 0)  # (batch_size, max_seq_length)\n", "    report_masks = torch.stack(report_masks, 0)  # (batch_size, max_seq_length)\n", "    \n", "    return image_ids, images, report_ids, report_masks\n", "\n", "def create_data_loaders(data, image_dir, tokenizer, batch_size=4, num_workers=2):\n", "    \"\"\"\n", "    创建训练、验证和测试数据加载器\n", "    \n", "    Args:\n", "        data (dict): 包含train/val/test分割的数据\n", "        image_dir (str): 图像目录\n", "        tokenizer: 分词器\n", "        batch_size (int): 批大小\n", "        num_workers (int): 数据加载进程数\n", "    \n", "    Returns:\n", "        tuple: (train_loader, val_loader, test_loader)\n", "    \"\"\"\n", "    # 创建数据集\n", "    train_dataset = IUXrayDataset(\n", "        data['train'], image_dir, tokenizer, \n", "        transform=create_image_transforms(is_training=True)\n", "    )\n", "    \n", "    val_dataset = IUXrayDataset(\n", "        data['val'], image_dir, tokenizer,\n", "        transform=create_image_transforms(is_training=False)\n", "    )\n", "    \n", "    test_dataset = IUXrayDataset(\n", "        data['test'], image_dir, tokenizer,\n", "        transform=create_image_transforms(is_training=False)\n", "    )\n", "    \n", "    # 创建数据加载器\n", "    train_loader = DataLoader(\n", "        train_dataset, batch_size=batch_size, shuffle=True,\n", "        collate_fn=collate_fn, num_workers=num_workers\n", "    )\n", "    \n", "    val_loader = DataLoader(\n", "        val_dataset, batch_size=batch_size, shuffle=False,\n", "        collate_fn=collate_fn, num_workers=num_workers\n", "    )\n", "    \n", "    test_loader = DataLoader(\n", "        test_dataset, batch_size=batch_size, shuffle=False,\n", "        collate_fn=collate_fn, num_workers=num_workers\n", "    )\n", "    \n", "    return train_loader, val_loader, test_loader\n", "\n", "print(\"数据加载器函数定义完成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 数据加载器演示\n", "\n", "让我们创建数据加载器并查看一个批次的数据："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["try:\n", "    # 创建数据加载器\n", "    train_loader, val_loader, test_loader = create_data_loaders(\n", "        data, image_dir, tokenizer, batch_size=2, num_workers=0  # 使用小批次和0个worker便于调试\n", "    )\n", "    \n", "    print(f\"数据加载器创建成功:\")\n", "    print(f\"训练集批次数: {len(train_loader)}\")\n", "    print(f\"验证集批次数: {len(val_loader)}\")\n", "    print(f\"测试集批次数: {len(test_loader)}\")\n", "    \n", "    # 获取一个训练批次\n", "    print(\"\\n获取一个训练批次...\")\n", "    for batch_idx, (image_ids, images, report_ids, report_masks) in enumerate(train_loader):\n", "        print(f\"\\n批次 {batch_idx + 1}:\")\n", "        print(f\"图像ID: {image_ids}\")\n", "        print(f\"图像张量形状: {images.shape}\")\n", "        print(f\"报告ID张量形状: {report_ids.shape}\")\n", "        print(f\"报告掩码张量形状: {report_masks.shape}\")\n", "        \n", "        # 显示第一个样本的详细信息\n", "        print(f\"\\n第一个样本详情:\")\n", "        print(f\"图像像素值范围: [{images[0].min():.3f}, {images[0].max():.3f}]\")\n", "        print(f\"报告ID (前20个): {report_ids[0][:20].tolist()}\")\n", "        print(f\"报告掩码 (前20个): {report_masks[0][:20].tolist()}\")\n", "        \n", "        # 解码报告\n", "        decoded_report = tokenizer.decode(report_ids[0].tolist())\n", "        print(f\"解码后的报告: {decoded_report}\")\n", "        \n", "        break  # 只查看第一个批次\n", "    \n", "    # 可视化批次中的图像\n", "    def visualize_batch(images, image_ids, max_samples=2):\n", "        \"\"\"\n", "        可视化批次中的图像\n", "        \n", "        Args:\n", "            images (torch.Tensor): 图像张量 (batch_size, 2, 3, 224, 224)\n", "            image_ids (list): 图像ID列表\n", "            max_samples (int): 最大显示样本数\n", "        \"\"\"\n", "        batch_size = min(images.shape[0], max_samples)\n", "        \n", "        fig, axes = plt.subplots(batch_size, 2, figsize=(10, 5 * batch_size))\n", "        if batch_size == 1:\n", "            axes = axes.reshape(1, -1)\n", "        \n", "        # 反标准化参数\n", "        mean = torch.tensor([0.485, 0.456, 0.406]).view(1, 3, 1, 1)\n", "        std = torch.tensor([0.229, 0.224, 0.225]).view(1, 3, 1, 1)\n", "        \n", "        for i in range(batch_size):\n", "            for j in range(2):  # 两张图像\n", "                # 反标准化\n", "                img = images[i, j] * std[0] + mean[0]\n", "                img = torch.clamp(img, 0, 1)\n", "                \n", "                # 转换为numpy并调整维度\n", "                img_np = img.permute(1, 2, 0).numpy()\n", "                \n", "                axes[i, j].imshow(img_np)\n", "                axes[i, j].set_title(f\"样本 {image_ids[i]} - 图像 {j+1}\")\n", "                axes[i, j].axis('off')\n", "        \n", "        plt.tight_layout()\n", "        plt.show()\n", "    \n", "    print(\"\\n可视化批次图像:\")\n", "    visualize_batch(images, image_ids)\n", "    \n", "except Exception as e:\n", "    print(f\"创建数据加载器时出错: {e}\")\n", "    print(\"这可能是因为图像文件不存在或路径不正确\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 数据预处理管道总结\n", "\n", "让我们总结一下完整的数据预处理管道："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_preprocessing_pipeline(annotation_path, image_dir, batch_size=4, max_seq_length=60):\n", "    \"\"\"\n", "    创建完整的数据预处理管道\n", "    \n", "    Args:\n", "        annotation_path (str): 标注文件路径\n", "        image_dir (str): 图像目录路径\n", "        batch_size (int): 批大小\n", "        max_seq_length (int): 最大序列长度\n", "    \n", "    Returns:\n", "        tuple: (tokenizer, train_loader, val_loader, test_loader)\n", "    \"\"\"\n", "    print(\"创建数据预处理管道...\")\n", "    \n", "    # 1. 加载数据\n", "    print(\"1. 加载数据...\")\n", "    with open(annotation_path, 'r') as f:\n", "        data = json.load(f)\n", "    \n", "    # 2. 创建分词器并构建词汇表\n", "    print(\"2. 构建词汇表...\")\n", "    tokenizer = MedicalReportTokenizer(threshold=3)\n", "    train_reports = [sample['report'] for sample in data['train']]\n", "    tokenizer.build_vocabulary(train_reports)\n", "    \n", "    # 3. 创建数据加载器\n", "    print(\"3. 创建数据加载器...\")\n", "    train_loader, val_loader, test_loader = create_data_loaders(\n", "        data, image_dir, tokenizer, batch_size=batch_size, num_workers=0\n", "    )\n", "    \n", "    print(\"数据预处理管道创建完成！\")\n", "    \n", "    # 打印统计信息\n", "    print(f\"\\n统计信息:\")\n", "    print(f\"词汇表大小: {tokenizer.get_vocab_size()}\")\n", "    print(f\"最大序列长度: {max_seq_length}\")\n", "    print(f\"批大小: {batch_size}\")\n", "    print(f\"训练批次数: {len(train_loader)}\")\n", "    print(f\"验证批次数: {len(val_loader)}\")\n", "    print(f\"测试批次数: {len(test_loader)}\")\n", "    \n", "    return tokenizer, train_loader, val_loader, test_loader\n", "\n", "# 演示完整管道\n", "try:\n", "    tokenizer, train_loader, val_loader, test_loader = create_preprocessing_pipeline(\n", "        annotation_path, image_dir, batch_size=2, max_seq_length=60\n", "    )\n", "    print(\"\\n✅ 数据预处理管道创建成功！\")\n", "except Exception as e:\n", "    print(f\"❌ 创建预处理管道失败: {e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 小结\n", "\n", "在本节中，我们学习了：\n", "\n", "### 图像预处理\n", "- **标准化**：统一图像尺寸和像素值范围\n", "- **数据增强**：随机裁剪、翻转等技术增加数据多样性\n", "- **归一化**：使用ImageNet统计值进行标准化\n", "\n", "### 文本预处理\n", "- **文本清理**：移除标点符号、统一格式\n", "- **分词**：将文本分割为词汇单元\n", "- **词汇表构建**：统计词频、过滤低频词\n", "- **序列编码**：将文本转换为数字序列\n", "\n", "### 数据加载\n", "- **Dataset类**：封装数据访问逻辑\n", "- **DataLoader**：实现批处理和并行加载\n", "- **批处理函数**：处理变长序列的对齐\n", "\n", "### 关键概念\n", "- **特殊标记**：`<start>`, `<end>`, `<pad>`, `<unk>`\n", "- **序列掩码**：区分真实内容和填充内容\n", "- **批处理**：提高训练效率的重要技术\n", "\n", "### 下一步\n", "在下一个notebook中，我们将深入学习CNN特征提取器，了解如何从医学影像中提取有意义的视觉特征。这是连接图像和文本的重要桥梁。"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}