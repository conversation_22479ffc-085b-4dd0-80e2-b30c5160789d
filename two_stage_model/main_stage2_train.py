#!/usr/bin/env python3
"""
二阶段训练主程序

功能：
1. 解析命令行参数
2. 初始化二阶段训练器
3. 执行训练和评估
"""

import argparse
import os
import sys
import time
import json
from pathlib import Path

# 添加R2Gen路径
sys.path.append('../R2Gen-main')

from stage2_trainer import Stage2Trainer


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='R2Gen 二阶段训练')
    
    # 数据相关参数
    parser.add_argument('--features_dir', type=str, default='features',
                       help='预计算特征文件目录')
    parser.add_argument('--ann_path', type=str, 
                       default='../R2Gen-main/data/iu_xray/annotation.json',
                       help='标注文件路径')
    parser.add_argument('--dataset_name', type=str, default='iu_xray',
                       help='数据集名称')
    
    # 模型相关参数
    parser.add_argument('--max_seq_length', type=int, default=60,
                       help='最大序列长度')
    parser.add_argument('--threshold', type=int, default=3,
                       help='词汇表阈值')
    parser.add_argument('--num_layers', type=int, default=3,
                       help='Transformer层数')
    parser.add_argument('--d_model', type=int, default=512,
                       help='模型维度')
    parser.add_argument('--d_ff', type=int, default=512,
                       help='前馈网络维度')
    parser.add_argument('--num_heads', type=int, default=8,
                       help='注意力头数')
    parser.add_argument('--dropout', type=float, default=0.1,
                       help='Dropout率')

    # 视觉提取器参数
    parser.add_argument('--visual_extractor', type=str, default='resnet101',
                       help='视觉提取器类型')
    parser.add_argument('--visual_extractor_pretrained', type=bool, default=True,
                       help='是否使用预训练的视觉提取器')
    parser.add_argument('--d_vf', type=int, default=2048,
                       help='视觉特征维度')

    # 其他模型参数
    parser.add_argument('--logit_layers', type=int, default=1,
                       help='logit层数')
    parser.add_argument('--bos_idx', type=int, default=0,
                       help='开始标记索引')
    parser.add_argument('--eos_idx', type=int, default=0,
                       help='结束标记索引')
    parser.add_argument('--pad_idx', type=int, default=0,
                       help='填充标记索引')
    parser.add_argument('--use_bn', type=int, default=0,
                       help='是否使用批归一化')
    parser.add_argument('--drop_prob_lm', type=float, default=0.5,
                       help='语言模型dropout率')
    parser.add_argument('--rm_num_slots', type=int, default=3,
                       help='关系记忆槽数')
    parser.add_argument('--rm_num_heads', type=int, default=8,
                       help='关系记忆头数')
    parser.add_argument('--rm_d_model', type=int, default=512,
                       help='关系记忆模型维度')
    parser.add_argument('--sample_method', type=str, default='beam_search',
                       help='采样方法')
    
    # 训练相关参数
    parser.add_argument('--batch_size', type=int, default=16,
                       help='批次大小')
    parser.add_argument('--epochs', type=int, default=100,
                       help='训练轮数')
    parser.add_argument('--lr_ed', type=float, default=1e-4,
                       help='编码器-解码器学习率')
    parser.add_argument('--weight_decay', type=float, default=5e-5,
                       help='权重衰减')
    parser.add_argument('--step_size', type=int, default=50,
                       help='学习率衰减步长')
    parser.add_argument('--gamma', type=float, default=0.1,
                       help='学习率衰减因子')
    parser.add_argument('--grad_clip', type=float, default=5.0,
                       help='梯度裁剪阈值')
    
    # 系统相关参数
    parser.add_argument('--num_workers', type=int, default=4,
                       help='数据加载工作进程数')
    parser.add_argument('--save_dir', type=str, default='results/stage2_training',
                       help='模型保存目录')
    parser.add_argument('--seed', type=int, default=9223,
                       help='随机种子')
    
    # 词汇表相关参数（兼容性）
    parser.add_argument('--vocab_path', type=str, 
                       default='../R2Gen-main/data/iu_xray/vocab.pkl',
                       help='词汇表路径')
    
    return parser.parse_args()


def set_seed(seed):
    """设置随机种子"""
    import random
    import numpy as np
    import torch
    
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False


def main():
    """主函数"""
    print("=== R2Gen 二阶段训练 ===\n")
    
    # 解析参数
    args = parse_arguments()
    
    # 设置随机种子
    set_seed(args.seed)
    print(f"随机种子: {args.seed}")
    
    # 验证特征目录
    features_dir = Path(args.features_dir)
    if not features_dir.exists():
        print(f"❌ 特征目录不存在: {features_dir}")
        return
    
    # 检查各个分割的特征文件
    for split in ['train', 'val', 'test']:
        split_dir = features_dir / split
        if not split_dir.exists():
            print(f"❌ {split} 特征目录不存在: {split_dir}")
            return
        
        feature_files = list(split_dir.glob('*.npy'))
        print(f"✅ {split} 集特征文件: {len(feature_files)} 个")
    
    # 验证标注文件
    if not os.path.exists(args.ann_path):
        print(f"❌ 标注文件不存在: {args.ann_path}")
        return
    
    print(f"✅ 标注文件: {args.ann_path}")
    
    # 创建保存目录
    os.makedirs(args.save_dir, exist_ok=True)
    print(f"✅ 保存目录: {args.save_dir}")
    
    # 保存训练参数
    args_dict = vars(args)
    with open(os.path.join(args.save_dir, 'training_args.json'), 'w') as f:
        json.dump(args_dict, f, indent=2)
    
    print(f"\n训练参数:")
    for key, value in args_dict.items():
        print(f"  {key}: {value}")
    
    # 记录开始时间
    start_time = time.time()
    
    try:
        # 初始化训练器
        print(f"\n初始化二阶段训练器...")
        trainer = Stage2Trainer(args)
        
        # 开始训练
        test_scores = trainer.train()
        
        # 记录结束时间
        end_time = time.time()
        training_time = end_time - start_time
        
        print(f"\n=== 训练完成 ===")
        print(f"总训练时间: {training_time:.2f} 秒")
        print(f"平均每轮时间: {training_time / args.epochs:.2f} 秒")
        
        # 保存最终结果
        results = {
            'training_time': training_time,
            'epochs': args.epochs,
            'best_val_bleu4': trainer.best_val_score,
            'test_scores': test_scores,
            'train_losses': trainer.train_losses,
            'val_scores': trainer.val_scores
        }
        
        with open(os.path.join(args.save_dir, 'training_results.json'), 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"✅ 结果已保存到: {args.save_dir}")
        
    except Exception as e:
        print(f"❌ 训练过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
