# IU X-ray数据集模型性能对比

本文档记录了不同模型配置在IU X-ray数据集上的性能表现，用于对比分析和后续实验参考。

## 数据集信息

- **数据集**: IU X-ray
- **训练集**: 2,069样本
- **验证集**: 296样本  
- **测试集**: 590样本
- **总计**: 2,955样本

## 模型性能对比表

### 各个论文的基准模型对比 (IU X-ray数据集)

| 模型 | BLEU-1 | BLEU-2 | BLEU-3 | BLEU-4 | METEOR | ROUGE-L | 备注 |
|------|--------|--------|--------|--------|--------|---------|------|
| **ST** | 0.2160 | 0.1240 | 0.0870 | 0.0660 | - | 0.3060 | Show & Tell |
| **ATT2IN** | 0.2240 | 0.1290 | 0.0890 | 0.0680 | - | 0.3080 | Attention模型 |
| **ADAATT** | 0.2200 | 0.1270 | 0.0890 | 0.0680 | - | 0.3080 | 自适应注意力 |
| **COATT** | 0.4550 | 0.2880 | 0.2050 | 0.1540 | - | 0.3690 | 协同注意力 |
| **HRGR** | 0.4380 | 0.2980 | 0.2080 | 0.1510 | - | 0.3220 | 层次化推理 |
| **CMAS-RL** | 0.4640 | 0.3010 | 0.2100 | 0.1540 | - | 0.3620 | 强化学习 |
| **R2Gen (论文)** | **0.4700** | **0.3040** | **0.2190** | **0.1650** | **0.1870** | **0.3710** | 论文展示结果 |
| **R2Gen (论文模型实测)** | **0.4879** | **0.3194** | **0.2324** | **0.1772** | **0.2022** | **0.3719** | 论文作者模型实测 |

### 我们的实验结果对比

| 模型配置 | 训练轮数 | 损失函数 | BLEU-1 | BLEU-2 | BLEU-3 | BLEU-4 | METEOR | ROUGE-L | CIDEr | 训练时间 |
|---------|---------|----------|--------|--------|--------|--------|--------|---------|-------|----------|
| **我们的R2Gen源码复现_0730_15点15分** | 5 | CE Loss | 0.4533 | 0.2731 | 0.1746 | 0.1246 | 0.1608 | 0.3778 | 0.3318 | ~300s |
| **原R2Gen编码器二阶段_0730_15点20分** | 2 | CE Loss | 0.3457 | 0.1937 | 0.1196 | 0.0770 | 0.1459 | 0.2746 | - | 96.5s |
| **我们的R2Gen源码复现_0730_18点25分** | 5 | CE Loss | 0.3621 | 0.2161 | 0.1527 | 0.1151 | 0.1389 | 0.3376 | - | ~300s |
| **原R2Gen编码器二阶段_0730_18点30分** | 5 | CE Loss | 0.3851 | 0.2199 | 0.1381 | 0.0912 | 0.1454 | 0.2890 | - | 292s |
| **修正的二阶段模型_0730_21点00分** | 5 | CE Loss | 0.0000 | 0.0000 | 0.0000 | 0.0000 | 0.0000 | 0.0000 | - | 模型异常 |
| **增强版二阶段模型_0731_13点00分** | 5 | CE Loss | 待测试 | 待测试 | 待测试 | 待测试 | 待测试 | 待测试 | - | 实现中 |
| **编码器A二阶段** | - | - | - | - | - | - | - | - | - | - |
| **编码器B二阶段** | - | - | - | - | - | - | - | - | - | - |
| **编码器C二阶段** | - | - | - | - | - | - | - | - | - | - |

**说明**: 我们的复现结果与论文结果存在差异是正常现象，可能由于：
- 训练参数设置差异
- 数据预处理方法不同
- 随机种子和初始化差异
- 硬件环境和框架版本差异



---

*本文档将随着新实验结果持续更新*

---
_2025-07-31_13:10:00_
