#!/usr/bin/env python3
"""
二阶段训练数据加载器

功能：
1. 加载预计算的视觉特征
2. 提供与原始数据加载器兼容的接口
3. 支持训练、验证和测试集
"""

import os
import json
import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader
from pathlib import Path


class Stage2Dataset(Dataset):
    """
    二阶段训练数据集
    
    加载预计算的视觉特征而不是原始图像
    """
    
    def __init__(self, features_dir, ann_path, split, tokenizer, max_seq_length=60, transform=None):
        """
        初始化数据集
        
        Args:
            features_dir (str): 预计算特征文件目录
            ann_path (str): 标注文件路径
            split (str): 数据集分割 ('train', 'val', 'test')
            tokenizer: 分词器
            max_seq_length (int): 最大序列长度
            transform: 数据变换（保持兼容性，实际不使用）
        """
        self.features_dir = Path(features_dir) / split
        self.split = split
        self.tokenizer = tokenizer
        self.max_seq_length = max_seq_length
        
        # 加载标注数据
        with open(ann_path, 'r') as f:
            self.ann = json.load(f)
        
        # 获取对应分割的数据
        self.examples = self.ann[split]
        
        print(f"Stage2Dataset 初始化完成:")
        print(f"  分割: {split}")
        print(f"  样本数: {len(self.examples)}")
        print(f"  特征目录: {self.features_dir}")
        
        # 验证特征文件是否存在
        self._verify_features()
    
    def _verify_features(self):
        """验证特征文件是否存在"""
        missing_files = []
        for example in self.examples[:10]:  # 只检查前10个文件
            feature_file = self.features_dir / f"{example['id']}.npy"
            if not feature_file.exists():
                missing_files.append(str(feature_file))
        
        if missing_files:
            print(f"警告: 发现 {len(missing_files)} 个缺失的特征文件（仅检查前10个）:")
            for file in missing_files[:5]:
                print(f"  {file}")
            if len(missing_files) > 5:
                print(f"  ... 还有 {len(missing_files) - 5} 个文件")
        else:
            print(f"✅ 特征文件验证通过（检查了前10个文件）")
    
    def __len__(self):
        """返回数据集大小"""
        return len(self.examples)
    
    def __getitem__(self, idx):
        """
        获取单个样本
        
        Returns:
            dict: 包含特征和标签的字典
                - att_feats: 注意力特征 (98, 2048)
                - fc_feats: 全连接特征 (4096,)
                - labels: 标签序列
                - masks: 掩码序列
        """
        example = self.examples[idx]
        
        # 加载预计算的特征
        feature_file = self.features_dir / f"{example['id']}.npy"
        
        try:
            features = np.load(feature_file, allow_pickle=True).item()
            att_feats = torch.FloatTensor(features['att_feats'])  # (98, 2048)
            fc_feats = torch.FloatTensor(features['fc_feats'])    # (4096,)
        except Exception as e:
            print(f"加载特征文件失败: {feature_file}")
            print(f"错误: {e}")
            # 返回零特征作为备用
            att_feats = torch.zeros(98, 2048)
            fc_feats = torch.zeros(4096)
        
        # 处理报告文本
        report = example['report']
        
        # 分词和编码
        tokens = self.tokenizer(report)
        
        # 截断或填充到指定长度
        if len(tokens) > self.max_seq_length:
            tokens = tokens[:self.max_seq_length]
        else:
            tokens = tokens + [0] * (self.max_seq_length - len(tokens))
        
        # 创建标签（用于计算损失）
        labels = tokens[1:] + [0]  # 向左移动一位
        
        # 创建掩码
        masks = [1 if token != 0 else 0 for token in tokens]
        
        return {
            'att_feats': att_feats,
            'fc_feats': fc_feats,
            'labels': torch.LongTensor(labels),
            'masks': torch.FloatTensor(masks)
        }


def create_stage2_dataloader(features_dir, ann_path, split, tokenizer, 
                           batch_size=16, shuffle=None, num_workers=4, 
                           max_seq_length=60):
    """
    创建二阶段训练数据加载器
    
    Args:
        features_dir (str): 特征文件目录
        ann_path (str): 标注文件路径
        split (str): 数据集分割
        tokenizer: 分词器
        batch_size (int): 批次大小
        shuffle (bool): 是否打乱数据，None时自动设置
        num_workers (int): 工作进程数
        max_seq_length (int): 最大序列长度
    
    Returns:
        DataLoader: 数据加载器
    """
    # 自动设置shuffle
    if shuffle is None:
        shuffle = (split == 'train')
    
    # 创建数据集
    dataset = Stage2Dataset(
        features_dir=features_dir,
        ann_path=ann_path,
        split=split,
        tokenizer=tokenizer,
        max_seq_length=max_seq_length
    )
    
    # 创建数据加载器
    dataloader = DataLoader(
        dataset,
        batch_size=batch_size,
        shuffle=shuffle,
        num_workers=num_workers,
        pin_memory=True,
        drop_last=(split == 'train')  # 训练时丢弃最后一个不完整的批次
    )
    
    print(f"Stage2DataLoader 创建完成:")
    print(f"  批次大小: {batch_size}")
    print(f"  打乱数据: {shuffle}")
    print(f"  工作进程: {num_workers}")
    print(f"  批次数量: {len(dataloader)}")
    
    return dataloader


def test_stage2_dataloader():
    """测试二阶段数据加载器"""
    print("=== 测试二阶段数据加载器 ===\n")
    
    # 模拟分词器
    class MockTokenizer:
        def __call__(self, text):
            # 简单的模拟分词
            words = text.lower().split()[:50]  # 限制长度
            return [1] + list(range(2, len(words) + 2)) + [2]  # 添加开始和结束标记

        def decode_batch(self, ids_batch):
            # 模拟解码
            return [f"decoded_report_{i}" for i in range(len(ids_batch))]
    
    tokenizer = MockTokenizer()
    
    # 测试参数
    features_dir = "features"
    ann_path = "../R2Gen-main/data/iu_xray/annotation.json"
    
    try:
        # 创建验证集数据加载器（样本较少，适合测试）
        dataloader = create_stage2_dataloader(
            features_dir=features_dir,
            ann_path=ann_path,
            split='val',
            tokenizer=tokenizer,
            batch_size=4,
            num_workers=0  # 测试时不使用多进程
        )
        
        # 测试加载一个批次
        print("\n--- 测试数据加载 ---")
        for i, batch in enumerate(dataloader):
            print(f"批次 {i+1}:")
            print(f"  att_feats 形状: {batch['att_feats'].shape}")
            print(f"  fc_feats 形状: {batch['fc_feats'].shape}")
            print(f"  labels 形状: {batch['labels'].shape}")
            print(f"  masks 形状: {batch['masks'].shape}")
            
            # 验证数据类型和值范围
            print(f"  att_feats 数据类型: {batch['att_feats'].dtype}")
            print(f"  att_feats 值范围: [{batch['att_feats'].min():.4f}, {batch['att_feats'].max():.4f}]")
            
            if i >= 2:  # 只测试前3个批次
                break
        
        print("\n✅ 二阶段数据加载器测试通过")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    test_stage2_dataloader()
