{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 01 - R2Gen项目概览与环境配置\n", "\n", "欢迎来到医学影像报告生成项目的学习之旅！本系列notebook旨在帮助您从零开始理解深度学习在医学影像报告生成中的应用。\n", "\n", "## 学习目标\n", "\n", "- 了解R2Gen项目的整体架构和工作原理\n", "- 配置必要的开发环境\n", "- 熟悉项目的文件结构和主要组件\n", "- 理解医学影像报告生成任务的基本概念\n", "\n", "## 项目介绍\n", "\n", "R2Gen（Radiology Report Generation）是一个基于深度学习的医学影像报告自动生成系统，发表于EMNLP-2020会议。该项目的核心思想是结合计算机视觉和自然语言处理技术，通过分析医学影像（如X光片）自动生成描述性的医学报告。\n", "\n", "### 项目特点\n", "\n", "- **记忆驱动的Transformer架构**：使用创新的关系记忆机制增强Transformer的生成能力\n", "- **多模态学习**：结合图像特征和文本生成\n", "- **医学专业领域适应**：针对放射学报告的特点进行了优化\n", "\n", "### 应用场景\n", "\n", "- 辅助放射科医生撰写报告，提高工作效率\n", "- 为缺乏专业放射科医生的地区提供初步诊断支持\n", "- 医学教育和培训工具\n", "- 医学研究数据分析"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 环境配置\n", "\n", "首先，我们需要确保安装了所有必要的依赖包。R2Gen项目主要基于PyTorch框架开发。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 检查Python版本\n", "import sys\n", "print(f\"Python版本: {sys.version}\")\n", "\n", "# 检查是否有GPU可用（对于深度学习非常重要）\n", "import torch\n", "print(f\"PyTorch版本: {torch.__version__}\")\n", "print(f\"CUDA是否可用: {torch.cuda.is_available()}\")\n", "if torch.cuda.is_available():\n", "    print(f\"GPU型号: {torch.cuda.get_device_name(0)}\")\n", "    print(f\"GPU数量: {torch.cuda.device_count()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 安装必要的依赖包\n", "\n", "根据项目README.md文件，我们需要安装以下依赖："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 如果需要安装依赖，请取消下面代码的注释并运行\n", "# !pip install torch==1.7.1 torchvision==0.8.2 opencv-python==********\n", "# !pip install matplotlib numpy pandas scikit-learn tqdm"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 项目结构解析\n", "\n", "R2Gen项目的文件结构组织得非常清晰，让我们来了解一下各个部分的功能："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 查看项目根目录结构\n", "import os\n", "\n", "def list_directory(path, indent=0):\n", "    \"\"\"递归列出目录结构\"\"\"\n", "    print(' ' * indent + os.path.basename(path) + '/')\n", "    try:\n", "        for item in sorted(os.listdir(path)):\n", "            if item.startswith('.'):\n", "                continue  # 跳过隐藏文件\n", "            item_path = os.path.join(path, item)\n", "            if os.path.isdir(item_path):\n", "                list_directory(item_path, indent + 2)\n", "            else:\n", "                print(' ' * (indent + 2) + item)\n", "    except PermissionError:\n", "        print(' ' * (indent + 2) + \"[权限不足，无法访问]\")\n", "\n", "# 获取当前工作目录的上一级目录（项目根目录）\n", "project_root = os.path.abspath(os.path.join(os.getcwd(), '..'))\n", "list_directory(project_root)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 主要目录和文件说明\n", "\n", "- **data/**: 存放数据集\n", "  - **iu_xray/**: IU X-Ray数据集\n", "    - **images/**: 存放X光图像\n", "    - **annotation.json**: 包含图像ID、报告文本和路径的标注文件\n", "  \n", "- **models/**: 模型定义\n", "  - **r2gen.py**: R2Gen模型的主要实现\n", "  \n", "- **modules/**: 模型的各个组件\n", "  - **visual_extractor.py**: 视觉特征提取器（基于CNN）\n", "  - **encoder_decoder.py**: 编码器-解码器架构（基于Transformer）\n", "  - **tokenizers.py**: 文本标记化工具\n", "  - **datasets.py**: 数据集加载和处理\n", "  - **dataloaders.py**: 数据加载器\n", "  - **metrics.py**: 评估指标\n", "  - **trainer.py**: 训练流程\n", "  - **tester.py**: 测试流程\n", "  \n", "- **main_train.py**: 训练脚本\n", "- **main_test.py**: 测试脚本\n", "- **main_plot.py**: 可视化脚本\n", "- **train_iu_xray.sh**: IU X-Ray数据集训练脚本\n", "- **test_iu_xray.sh**: IU X-Ray数据集测试脚本\n", "\n", "- **pycocoevalcap/**: 评估指标库，用于计算BLEU、METEOR、ROUGE等NLP评估指标"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 模型架构概览\n", "\n", "R2Gen模型的整体架构如下图所示：\n", "\n", "```\n", "                   +-------------------+\n", "                   |    医学影像输入    |\n", "                   +-------------------+\n", "                             |\n", "                             v\n", "                   +-------------------+\n", "                   |  CNN特征提取器    |  (ResNet101)\n", "                   +-------------------+\n", "                             |\n", "                             v\n", "                   +-------------------+\n", "                   |  视觉特征表示     |\n", "                   +-------------------+\n", "                             |\n", "                             v\n", "+----------------+  +-------------------+  +----------------+\n", "|  关系记忆模块  | <-|  Transformer编码器 | ->|  注意力机制   |\n", "+----------------+  +-------------------+  +----------------+\n", "        |                     |                    |\n", "        v                     v                    v\n", "+----------------+  +-------------------+  +----------------+\n", "|  记忆增强      | <-|  Transformer解码器 | <-|  文本嵌入     |\n", "+----------------+  +-------------------+  +----------------+\n", "                             |\n", "                             v\n", "                   +-------------------+\n", "                   |    生成的报告     |\n", "                   +-------------------+\n", "```\n", "\n", "### 主要组件\n", "\n", "1. **视觉特征提取器**：使用预训练的ResNet101从医学影像中提取视觉特征\n", "2. **Transformer编码器**：处理视觉特征，生成上下文表示\n", "3. **关系记忆模块**：增强模型记忆能力，捕获长期依赖关系\n", "4. **Transformer解码器**：结合视觉特征和记忆模块，生成报告文本\n", "5. **注意力机制**：在解码过程中关注相关的视觉特征"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 数据集概览\n", "\n", "在本学习系列中，我们将使用IU X-Ray数据集。这是一个公开的胸部X光片数据集，包含了X光图像和对应的放射学报告。\n", "\n", "### IU X-Ray数据集特点\n", "\n", "- 包含约7,470张X光图像和3,955份报告\n", "- 每个病例通常有2张不同角度（正面和侧面）的X光片\n", "- 报告包含发现（findings）和印象（impression）部分\n", "\n", "让我们简单查看一下数据集的结构："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "# 加载标注文件\n", "annotation_path = os.path.join(project_root, 'data', 'iu_xray', 'annotation.json')\n", "try:\n", "    with open(annotation_path, 'r') as f:\n", "        annotations = json.load(f)\n", "    \n", "    # 打印数据集统计信息\n", "    print(f\"训练集样本数: {len(annotations.get('train', []))}\")\n", "    print(f\"验证集样本数: {len(annotations.get('val', []))}\")\n", "    print(f\"测试集样本数: {len(annotations.get('test', []))}\")\n", "    \n", "    # 显示一个样本示例\n", "    if annotations.get('train'):\n", "        example = annotations['train'][0]\n", "        print(\"\\n样本示例:\")\n", "        print(f\"ID: {example['id']}\")\n", "        print(f\"图像路径: {example['image_path']}\")\n", "        print(f\"报告: {example['report'][:200]}...\" if len(example['report']) > 200 else example['report'])\n", "except FileNotFoundError:\n", "    print(f\"找不到标注文件: {annotation_path}\")\n", "    print(\"请确保已下载IU X-Ray数据集并放置在正确位置\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 训练和测试流程概览\n", "\n", "R2Gen项目的训练和测试流程如下：\n", "\n", "### 训练流程\n", "\n", "1. **数据准备**：加载和预处理医学影像和报告文本\n", "2. **模型初始化**：创建R2Gen模型实例\n", "3. **训练循环**：\n", "   - 前向传播：模型处理输入图像，生成报告\n", "   - 计算损失：比较生成的报告和真实报告\n", "   - 反向传播：更新模型参数\n", "4. **验证**：在验证集上评估模型性能\n", "5. **保存模型**：保存训练好的模型权重\n", "\n", "### 测试流程\n", "\n", "1. **加载模型**：加载训练好的模型权重\n", "2. **生成报告**：模型处理测试集图像，生成报告\n", "3. **评估性能**：计算BLEU、METEOR、ROUGE等评估指标\n", "4. **可视化结果**：生成注意力热图等可视化结果"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 简单示例：加载预训练模型\n", "\n", "让我们尝试加载一个预训练的R2Gen模型，了解模型的基本结构："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "sys.path.append(project_root)  # 添加项目根目录到Python路径\n", "\n", "import torch\n", "import argparse\n", "from models.r2gen import R2GenModel\n", "from modules.tokenizers import Tokenizer\n", "\n", "# 创建一个简单的参数对象\n", "class Args:\n", "    def __init__(self):\n", "        self.ann_path = os.path.join(project_root, 'data', 'iu_xray', 'annotation.json')\n", "        self.dataset_name = 'iu_xray'\n", "        self.threshold = 3  # 词汇表阈值\n", "        self.max_seq_length = 60  # 最大序列长度\n", "        self.visual_extractor = 'resnet101'  # 视觉特征提取器\n", "        self.visual_extractor_pretrained = True  # 使用预训练的视觉特征提取器\n", "        self.d_model = 512  # Transformer模型维度\n", "        self.d_ff = 512  # 前馈网络维度\n", "        self.num_heads = 8  # 注意力头数\n", "        self.num_layers = 3  # Transformer层数\n", "        self.dropout = 0.1  # Dropout率\n", "        self.rm_num_slots = 3  # 关系记忆槽数\n", "        self.rm_num_heads = 8  # 关系记忆注意力头数\n", "        self.rm_d_model = 512  # 关系记忆模型维度\n", "\n", "args = Args()\n", "\n", "try:\n", "    # 创建分词器\n", "    tokenizer = Tokenizer(args)\n", "    \n", "    # 创建模型\n", "    model = R2GenModel(args, tokenizer)\n", "    \n", "    # 打印模型结构\n", "    print(model)\n", "    \n", "    # 打印模型参数数量\n", "    model_parameters = filter(lambda p: p.requires_grad, model.parameters())\n", "    params = sum([torch.numel(p) for p in model_parameters])\n", "    print(f\"\\n可训练参数数量: {params:,}\")\n", "except Exception as e:\n", "    print(f\"加载模型时出错: {e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 下一步学习计划\n", "\n", "在接下来的学习中，我们将深入探索R2Gen项目的各个组件：\n", "\n", "1. **数据集理解与探索**：深入了解IU X-Ray数据集的结构和特点\n", "2. **数据预处理与加载**：学习如何处理医学影像和文本数据\n", "3. **CNN特征提取器**：理解如何从医学影像中提取视觉特征\n", "4. **Transformer架构**：学习编码器-解码器机制\n", "5. **记忆驱动机制**：深入了解R2Gen的核心创新\n", "6. **训练与评估**：实现完整的训练和测试流程\n", "\n", "## 小结\n", "\n", "在本节中，我们：\n", "\n", "- 了解了R2Gen项目的整体架构和工作原理\n", "- 配置了必要的开发环境\n", "- 熟悉了项目的文件结构和主要组件\n", "- 初步了解了医学影像报告生成任务\n", "\n", "下一节，我们将深入探索IU X-Ray数据集，了解医学影像和报告的特点。"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}